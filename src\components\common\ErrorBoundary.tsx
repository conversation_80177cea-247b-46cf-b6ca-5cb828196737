import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, <PERSON>pography, Button, Alert, AlertTitle } from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import RefreshIcon from '@mui/icons-material/Refresh';
import HomeIcon from '@mui/icons-material/Home';
import ProfessionalButton from './ProfessionalButton';
import { generateErrorId } from '../../utils/errorModalUtils';
import config from '../../config';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: generateErrorId()
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to external service if needed
    this.logErrorToService(error, errorInfo);
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // Log error details for debugging
    const errorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    console.error('Error Boundary - Full Error Details:', errorDetails);

    // Here you could send to an external logging service
    // Example: Sentry, LogRocket, etc.
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  private handleGoHome = () => {
    // Clear error state and navigate to home
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });

    // Reload the page to ensure clean state
    window.location.href = '/';
  };

  private isTimeoutError = (error: Error): boolean => {
    return error.message.toLowerCase().includes('timeout') ||
           error.message.toLowerCase().includes('timed out') ||
           error.name === 'TimeoutError' ||
           error.message.toLowerCase().includes('network error') ||
           error.message.toLowerCase().includes('fetch');
  };

  private getErrorMessage = (error: Error): { title: string; message: string; isTimeout: boolean } => {
    const isTimeout = this.isTimeoutError(error);
    
    if (isTimeout) {
      return {
        title: 'Request Timeout',
        message: 'The request is taking longer than expected. This might be due to a slow internet connection or server processing time. Please try again.',
        isTimeout: true
      };
    }

    // Check for specific error types
    if (error.message.toLowerCase().includes('network')) {
      return {
        title: 'Network Error',
        message: 'Unable to connect to our servers. Please check your internet connection and try again.',
        isTimeout: false
      };
    }

    if (error.message.toLowerCase().includes('chunk')) {
      return {
        title: 'Loading Error',
        message: 'There was an issue loading part of the application. Please refresh the page and try again.',
        isTimeout: false
      };
    }

    return {
      title: 'Something went wrong',
      message: 'An unexpected error occurred. Our team has been notified and is working to fix this issue.',
      isTimeout: false
    };
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const error = this.state.error!;
      const { title, message, isTimeout } = this.getErrorMessage(error);

      return (
        <Box sx={{
          width: '100%',
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          p: 3,
          backgroundColor: '#f8f9fa'
        }}>
          <Box sx={{
            textAlign: 'center',
            maxWidth: 600,
            p: 4,
            borderRadius: 3,
            backgroundColor: 'white',
            border: '1px solid #e0e0e0',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
          }}>
            {/* Error Icon */}
            <Box sx={{
              display: 'flex',
              justifyContent: 'center',
              mb: 2
            }}>
              <ErrorOutlineIcon sx={{
                fontSize: '4rem',
                color: isTimeout ? '#ff9800' : '#e53e3e'
              }} />
            </Box>

            {/* Error Title */}
            <Typography variant="h4" sx={{
              color: isTimeout ? '#e65100' : '#e53e3e',
              mb: 2,
              fontWeight: 600
            }}>
              {title}
            </Typography>

            {/* Error Message */}
            <Typography variant="body1" sx={{
              color: '#2d3748',
              mb: 3,
              lineHeight: 1.6
            }}>
              {message}
            </Typography>

            {/* Error ID for support */}
            <Alert severity="info" sx={{ mb: 3, textAlign: 'left' }}>
              <AlertTitle>Error ID</AlertTitle>
              <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                {this.state.errorId}
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                Please include this ID when contacting support.
              </Typography>
            </Alert>

            {/* Action Buttons */}
            <Box sx={{
              display: 'flex',
              gap: 2,
              justifyContent: 'center',
              flexWrap: 'wrap'
            }}>
              <ProfessionalButton
                variant="contained"
                startIcon={<RefreshIcon />}
                onClick={this.handleRetry}
                color="primary"
              >
                Try Again
              </ProfessionalButton>

              <ProfessionalButton
                variant="outlined"
                startIcon={<HomeIcon />}
                onClick={this.handleGoHome}
              >
                Go to Home
              </ProfessionalButton>
            </Box>

            {/* Technical Details (only in development) */}
            {this.props.showDetails && process.env.NODE_ENV === 'development' && (
              <Box sx={{ mt: 3, textAlign: 'left' }}>
                <Alert severity="warning">
                  <AlertTitle>Technical Details (Development Only)</AlertTitle>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
                    <strong>Error:</strong> {error.message}
                  </Typography>
                  {error.stack && (
                    <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem', mt: 1 }}>
                      <strong>Stack:</strong><br />
                      {error.stack.split('\n').slice(0, 5).join('\n')}
                    </Typography>
                  )}
                </Alert>
              </Box>
            )}
          </Box>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
