<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DChartify - Simple Mode</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #ffffff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }
        .hero {
            padding: 60px 20px;
        }
        .hero h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            color: #1a1a1a;
            line-height: 1.2;
        }
        .hero p {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
        }
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        .chart-item {
            border-radius: 8px;
            box-shadow: 0 12px 24px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-4px);
        }
        .chart-item img {
            width: 100%;
            height: 150px;
            object-fit: contain;
            border-radius: 8px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3B82F6;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: background-color 0.3s ease;
        }
        .btn:hover {
            background-color: #2563EB;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 60px 0;
        }
        .feature {
            padding: 30px;
            background: #F8FAFC;
            border-radius: 12px;
            text-align: left;
        }
        .feature h3 {
            color: #1a1a1a;
            margin-bottom: 15px;
        }
        .feature p {
            color: #666;
            line-height: 1.6;
        }
        .status {
            background: #FEF3C7;
            border: 1px solid #F59E0B;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .status h3 {
            color: #92400E;
            margin: 0 0 10px 0;
        }
        .status p {
            color: #92400E;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="status">
            <h3>🚧 Development Mode - Simple Version</h3>
            <p>This is a simplified version of the home page without WebSocket/HMR issues. All images and basic functionality work correctly.</p>
        </div>

        <div class="hero">
            <h1>Turn Boring Spreadsheets into<br>Stunning Visual Stories in Seconds</h1>
            <p>With Your Data, Beautifully Chartified</p>
            
            <div class="charts-grid">
                <div class="chart-item">
                    <img src="/chart1.png" alt="Sample chart 1 - Data visualization example" />
                </div>
                <div class="chart-item">
                    <img src="/chart2.png" alt="Sample chart 2 - Data visualization example" />
                </div>
                <div class="chart-item">
                    <img src="/chart3.png" alt="Sample chart 3 - Data visualization example" />
                </div>
                <div class="chart-item">
                    <img src="/chart4.png" alt="Sample chart 4 - Data visualization example" />
                </div>
            </div>

            <a href="#" class="btn" onclick="alert('This is a simplified demo. The full React app would load here.')">Get Started Free</a>
        </div>

        <div class="features">
            <div class="feature">
                <h3>📊 Instant Chart Generation</h3>
                <p>Upload your Excel or CSV files and see beautiful charts generated instantly with smart formatting and professional styling.</p>
            </div>
            <div class="feature">
                <h3>🎨 Customizable Design</h3>
                <p>Modify chart titles, axis labels, colors, and styling to match your presentation needs perfectly.</p>
            </div>
            <div class="feature">
                <h3>🤖 AI-Powered Insights</h3>
                <p>Get intelligent analysis with executive summaries, data insights, hidden patterns, and actionable recommendations.</p>
            </div>
            <div class="feature">
                <h3>📱 Export & Share</h3>
                <p>Copy charts to clipboard for presentations or download as high-quality PNG images for any use case.</p>
            </div>
        </div>

        <div style="margin-top: 60px; padding: 20px; background: #F3F4F6; border-radius: 8px;">
            <p style="margin: 0; font-style: italic; color: #666;">
                Your Data is not stored anywhere, not used for any training, only for preparing charts.
            </p>
        </div>
    </div>

    <script>
        // Simple JavaScript to test functionality
        console.log('✅ Simple HTML version loaded successfully!');
        console.log('✅ All images should be visible without WebSocket errors');
        
        // Test image loading
        const images = document.querySelectorAll('img');
        images.forEach((img, index) => {
            img.onload = () => {
                console.log(`✅ Image ${index + 1} loaded successfully: ${img.src}`);
            };
            img.onerror = () => {
                console.error(`❌ Failed to load image ${index + 1}: ${img.src}`);
            };
        });
    </script>
</body>
</html>
