import React from 'react';
import { Box, Typography, Link as MuiLink } from '@mui/material';

export type PanelType = 'data' | 'charts';

interface LeftPanelNavProps {
  currentPanel: PanelType;
  onPanelChange: (panel: PanelType) => void;
}

const LeftPanelNav: React.FC<LeftPanelNavProps> = ({ currentPanel, onPanelChange }) => {
  return (
    <Box sx={{
      width: { xs: '60px', sm: '60px', md: '200px' }, // Responsive width: mobile/tablet 60px, desktop 200px
      minHeight: '100vh',
      backgroundColor: '#ffffff',
      borderRight: '1px solid rgba(0, 0, 0, 0.08)',
      boxShadow: '1px 0 3px rgba(0,0,0,0.06)',
      display: 'flex',
      flexDirection: 'column',
      p: { xs: 1, sm: 1, md: 3 }, // Responsive padding
      gap: 2,
      position: 'fixed', // Fixed position
      top: '80px', // Below navigation bar
      left: 0,
      zIndex: 1200,
    }}>


      {/* Navigation Links */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <MuiLink
          href="#"
          underline="none"
          onClick={() => onPanelChange('data')}
          title="Data" // Tooltip for mobile
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: { xs: 'center', sm: 'center', md: 'flex-start' }, // Center on mobile, left on desktop
            gap: { xs: 0, sm: 0, md: 2 }, // No gap on mobile, gap on desktop
            p: { xs: 1.5, sm: 1.5, md: 2 }, // Smaller padding on mobile
            borderRadius: 2,
            color: currentPanel === 'data' ? '#2196f3' : '#4a5568',
            backgroundColor: currentPanel === 'data' ? 'rgba(33, 150, 243, 0.08)' : 'transparent',
            fontWeight: currentPanel === 'data' ? 600 : 500,
            fontSize: '0.95rem',
            fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              backgroundColor: currentPanel === 'data' ? 'rgba(33, 150, 243, 0.12)' : 'rgba(0, 0, 0, 0.04)',
              transform: { xs: 'none', sm: 'none', md: 'translateX(2px)' }, // No transform on mobile
              color: '#2196f3',
            },
          }}
        >
          <Typography sx={{ fontSize: 24, fontWeight: 'bold' }}>📊</Typography>
          <Typography variant="body1" sx={{ fontWeight: 'inherit', display: { xs: 'none', sm: 'none', md: 'block' } }}>
            Data
          </Typography>
        </MuiLink>

        <MuiLink
          href="#"
          underline="none"
          onClick={() => onPanelChange('charts')}
          title="Charts" // Tooltip for mobile
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: { xs: 'center', sm: 'center', md: 'flex-start' }, // Center on mobile, left on desktop
            gap: { xs: 0, sm: 0, md: 2 }, // No gap on mobile, gap on desktop
            p: { xs: 1.5, sm: 1.5, md: 2 }, // Smaller padding on mobile
            borderRadius: 2,
            color: currentPanel === 'charts' ? '#2196f3' : '#4a5568',
            backgroundColor: currentPanel === 'charts' ? 'rgba(33, 150, 243, 0.08)' : 'transparent',
            fontWeight: currentPanel === 'charts' ? 600 : 500,
            fontSize: '0.95rem',
            fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              backgroundColor: currentPanel === 'charts' ? 'rgba(33, 150, 243, 0.12)' : 'rgba(0, 0, 0, 0.04)',
              transform: { xs: 'none', sm: 'none', md: 'translateX(2px)' }, // No transform on mobile
              color: '#2196f3',
            },
          }}
        >
          <Typography sx={{ fontSize: 24, fontWeight: 'bold' }}>📈</Typography>
          <Typography variant="body1" sx={{ fontWeight: 'inherit', display: { xs: 'none', sm: 'none', md: 'block' } }}>
            Charts
          </Typography>
        </MuiLink>
      </Box>
    </Box>
  );
};

export default LeftPanelNav;