@echo off
REM Batch script to restart development server on Windows
REM This helps resolve EMFILE (too many open files) errors

echo 🔄 Restarting development server for Windows...

REM Kill any existing Node.js processes
echo 🛑 Stopping existing Node.js processes...
taskkill /F /IM node.exe >nul 2>&1

REM Wait for processes to terminate
timeout /t 2 /nobreak >nul

REM Clear npm cache
echo 🧹 Clearing npm cache...
npm cache clean --force

REM Clear Vite cache
echo 🧹 Clearing Vite cache...
if exist "node_modules\.vite" rmdir /s /q "node_modules\.vite"

REM Clear dist folder
echo 🧹 Clearing dist folder...
if exist "dist" rmdir /s /q "dist"

echo ✅ Cleanup complete!

REM Start development server with Windows optimizations
echo 🚀 Starting development server with Windows optimizations...
set NODE_OPTIONS=--max-old-space-size=4096
npm run dev

echo 🎉 Development server started!
pause
