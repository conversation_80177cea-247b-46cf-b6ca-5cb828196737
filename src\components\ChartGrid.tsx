import React, { useState } from "react";
import { Box, Paper, Typography, List, ListItem, ListItemIcon, ListItemText, IconButton, Snackbar } from '@mui/material';
import CircleIcon from '@mui/icons-material/Circle';
import CloseIcon from '@mui/icons-material/Close';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import Lazy<PERSON>hart from './LazyChart';
import ErrorDisplay from './common/ErrorDisplay';
import ChartPreviewModal from './common/ChartPreviewModal';
import { getChartInsights, updateChart, ApiError } from '../services/apiService';
import ProfessionalButton from './common/ProfessionalButton';
import useAnalytics from '../hooks/useAnalytics';

// Export ChartData interface for use in Dashboard and other components
export interface ChartData {
  id: string | number; // Expect an ID for the key
  chart_type: string;
  chart_group?: string; // New field for grouping charts
  library: string;
  data: any;
  layout: {
    title?: string | { text: string };
    xaxis?: { title?: string | { text: string } };
    yaxis?: { title?: string | { text: string }; side?: 'left' | 'right' };
  };
  fields?: {
    x?: string;
    y?: string;
    numeric?: string;
    agg?: string;
  };
  available_fields?: {
    categorical?: string[];
    numerical?: string[];
    datetime?: string[];
  };
  allowed_chart_types?: string[] | string; // New field from backend API - can be array or comma-separated string
}

interface ChartGridProps {
  charts: ChartData[];
  availableColumns: string[]; // Keep if needed for Chart component edits
  onChartUpdate?: (index: number, updatedChart: any) => void; // Callback to update state in Dashboard
  gridBackgroundColor?: string;
  onGroupClick?: (groupName: string, charts: ChartData[]) => void; // Callback to navigate to group page
  onInsightsClick?: (chartData: ChartData) => void; // Callback for insights page navigation
  onModifyClick?: (chartData: ChartData) => void; // Callback for chart modification page
}

const ChartGrid: React.FC<ChartGridProps> = ({
  charts,
  onChartUpdate,
  onGroupClick,
  onInsightsClick,
  onModifyClick
}) => {
  const [insightsData, setInsightsData] = useState<{ chartId: string | number, insights: string[], position: 'left' | 'center' | 'right' } | null>(null);
  const [error, setError] = useState<ApiError | null>(null);
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [previewChartData, setPreviewChartData] = useState<ChartData | null>(null);

  // Analytics hook
  const { trackAdditionalChartsView } = useAnalytics();

  const clearError = () => setError(null);

  // Handle preview click
  const handlePreviewClick = (chartData: ChartData) => {
    setPreviewChartData(chartData);
    setPreviewModalOpen(true);
  };

  // Handle preview modal close
  const handlePreviewClose = () => {
    setPreviewModalOpen(false);
    setPreviewChartData(null);
  };
  const handleChartUpdate = async (chartIndex: number, updatedChart: any) => {
    if (!onChartUpdate) return;

    try {
      const updatedChartData = await updateChart(charts[chartIndex].id, updatedChart);
      onChartUpdate(chartIndex, updatedChartData);
    } catch (error) {
      console.error('Error updating chart:', error);
      // Set error for display
      if (error && typeof error === 'object' && 'message' in error) {
        setError(error as ApiError);
      } else {
        setError({
          message: 'Failed to update chart. Please try again.',
          type: 'error'
        });
      }
    }
  };

  const handleInsightsRequest = async (chartId: string | number, chartData: any, position: 'left' | 'center' | 'right') => {
    try {
      // Call the API service to get insights
      const insights = await getChartInsights(chartId, chartData);

      console.log('Chart insights received:', insights);

      // Update state to display insights
      setInsightsData({
        chartId,
        insights: insights.insights,
        position
      });

      return insights;
    } catch (error) {
      console.error('Error getting chart insights:', error);
      // Set error for display
      if (error && typeof error === 'object' && 'message' in error) {
        setError(error as ApiError);
      } else {
        setError({
          message: 'Failed to generate insights. Please try again.',
          type: 'error'
        });
      }
      return null;
    }
  };

  // Group charts by chart_group
  const groupedCharts = charts.reduce((groups, chart) => {
    const group = chart.chart_group || 'ungrouped';
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(chart);
    return groups;
  }, {} as Record<string, ChartData[]>);

  // Get the first chart from each group for display
  const displayCharts = Object.entries(groupedCharts).map(([groupName, groupCharts]) => ({
    groupName,
    chart: groupCharts[0], // First chart from the group
    totalCharts: groupCharts.length,
    allCharts: groupCharts
  }));

  // Responsive chart layout - no need to group into rows, let CSS Grid handle it

  return (
    <Box
      sx={{
        width: '100%',
        display: 'grid',
        gridTemplateColumns: {
          xs: '1fr', // Single column on mobile
          sm: '1fr', // Single column on small tablets
          md: 'repeat(2, 1fr)', // Two columns on medium screens and up
        },
        gap: 2,
        '@media (max-width: 900px)': {
          gridTemplateColumns: '1fr', // Force single column below 900px
        }
      }}
    >
      {/* Render each chart group */}
      {displayCharts.map((chartGroup, chartIndex) => {
        const chartHasInsights = insightsData && insightsData.chartId === chartGroup.chart?.id;

        // If this chart has insights, show them in a modal or overlay
        if (chartHasInsights) {
          return (
            <Paper
              key={`chart-${chartIndex}`}
              elevation={0}
              sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                borderRadius: 2,
                padding: 3,
                width: '100%', // Full width in grid
                aspectRatio: '1.5/1',
                display: 'flex',
                flexDirection: 'column',
                position: 'relative',
                overflow: 'auto',
                boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Chart Insights
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => setInsightsData(null)}
                  aria-label="close insights"
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>

              <List dense>
                {insightsData.insights.map((insight, i) => (
                  <ListItem key={i} alignItems="flex-start">
                    <ListItemIcon sx={{ minWidth: '24px' }}>
                      <CircleIcon sx={{ fontSize: 8 }} />
                    </ListItemIcon>
                    <ListItemText primary={insight} />
                  </ListItem>
                ))}
              </List>
            </Paper>
          );
        }

        // Show chart normally with "More Charts" button if there are multiple charts in the group
        return chartGroup && (
          <Paper
            key={`chart-${chartIndex}`}
            elevation={0}
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              borderRadius: 2,
              overflow: 'hidden', // Prevent horizontal scrollbars
              width: '100%', // Full width in grid
              aspectRatio: '1.4/1', // Increased aspect ratio for larger chart image
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
              boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
              p: 0.5, // Further reduced padding for more chart space
            }}
          >
            <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0 }}>
              <LazyChart
                data={chartGroup.chart}
                position={chartIndex % 2 === 0 ? "left" : "right"} // Alternate position based on index
                onChartUpdate={(updatedChart) => handleChartUpdate(chartIndex, updatedChart)}
                onInsightsRequest={handleInsightsRequest}
                onInsightsClick={onInsightsClick}
                onPreviewClick={handlePreviewClick}
                onModifyClick={onModifyClick}
                width="100%"
                height="100%"
                loadImmediately={chartIndex === 0} // Load first chart immediately, others lazily
                delay={chartIndex * 100} // Stagger loading with small delays
              />
            </Box>

            {/* More Charts Button - Positioned with action buttons */}
            {chartGroup.totalCharts > 1 && (
              <Box
                sx={{
                  position: 'absolute',
                  bottom: 8, // Align with action buttons row
                  right: 8,
                  zIndex: 3,
                }}
              >
                <ProfessionalButton
                  variant="primary"
                  size="small"
                  startIcon={<MoreHorizIcon />}
                  onClick={() => {
                    // Navigate immediately for better UX
                    onGroupClick?.(chartGroup.groupName, chartGroup.allCharts);

                    // Track additional charts view asynchronously (fire-and-forget)
                    trackAdditionalChartsView(
                      chartGroup.groupName,
                      chartGroup.totalCharts,
                      {
                        main_chart_type: chartGroup.chart.chart_type,
                        main_chart_id: chartGroup.chart.id
                      }
                    ).catch(error => {
                      console.warn('Analytics tracking failed:', error);
                    });
                  }}
                  sx={{
                    fontSize: '0.75rem',
                    padding: '3px 6px',
                    minWidth: 'auto',
                    height: '24px', // Match action button height
                  }}
                >
                  +{chartGroup.totalCharts - 1}
                </ProfessionalButton>
              </Box>
            )}
          </Paper>
        );
      })}

      {/* Error Snackbar */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={clearError}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <ErrorDisplay
          error={error || 'An error occurred'}
          variant="snackbar"
        />
      </Snackbar>

      {/* Chart Preview Modal */}
      <ChartPreviewModal
        open={previewModalOpen}
        onClose={handlePreviewClose}
        chartData={previewChartData}
      />
    </Box>
  );
};

export default ChartGrid;
