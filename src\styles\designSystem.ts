/**
 * Global Design System Constants
 * 
 * This file contains all the design tokens for consistent styling across the application.
 * Use these constants instead of hardcoded values to maintain design consistency.
 */

// Color Palette
export const colors = {
  // Background
  background: '#FFFFFF',
  
  // Text Colors
  text: {
    primary: '#2E2E2E',    // Neutral dark - main text
    secondary: '#7A7A7A',  // Secondary text, labels, captions
  },
  
  // Accent Colors
  accent: {
    primary: '#3B82F6',    // Sky Blue - primary actions, links
    secondary: '#10B981',  // Emerald Green - success, secondary actions
  },
  
  // Semantic Colors (derived from accent colors)
  semantic: {
    success: '#10B981',    // Emerald Green
    error: '#EF4444',      // Red
    warning: '#F59E0B',    // Amber
    info: '#3B82F6',       // Sky Blue
  },
  
  // Neutral Grays (for borders, dividers, etc.)
  neutral: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },
} as const;

// Typography
export const typography = {
  fontFamily: {
    primary: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica", "Arial", sans-serif',
    mono: '"SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Consolas", monospace',
  },
  
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
  },
  
  fontWeight: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
  },
  
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.6,
    loose: 2,
  },
  
  letterSpacing: {
    tight: '-0.02em',
    normal: '0',
    wide: '0.025em',
  },
} as const;

// Spacing (based on 4px grid)
export const spacing = {
  0: '0',
  1: '0.25rem',   // 4px
  2: '0.5rem',    // 8px
  3: '0.75rem',   // 12px
  4: '1rem',      // 16px
  5: '1.25rem',   // 20px
  6: '1.5rem',    // 24px
  8: '2rem',      // 32px
  10: '2.5rem',   // 40px
  12: '3rem',     // 48px
  16: '4rem',     // 64px
  20: '5rem',     // 80px
  24: '6rem',     // 96px
  32: '8rem',     // 128px
} as const;

// Border Radius
export const borderRadius = {
  none: '0',
  sm: '0.125rem',   // 2px
  base: '0.25rem',  // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  '3xl': '1.5rem',  // 24px
  full: '9999px',
} as const;

// Shadows
export const shadows = {
  sm: '0 1px 2px 0 rgba(46, 46, 46, 0.05)',
  base: '0 1px 3px 0 rgba(46, 46, 46, 0.1), 0 1px 2px 0 rgba(46, 46, 46, 0.06)',
  md: '0 4px 6px -1px rgba(46, 46, 46, 0.1), 0 2px 4px -1px rgba(46, 46, 46, 0.06)',
  lg: '0 10px 15px -3px rgba(46, 46, 46, 0.1), 0 4px 6px -2px rgba(46, 46, 46, 0.05)',
  xl: '0 20px 25px -5px rgba(46, 46, 46, 0.1), 0 10px 10px -5px rgba(46, 46, 46, 0.04)',
  '2xl': '0 25px 50px -12px rgba(46, 46, 46, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(46, 46, 46, 0.06)',
} as const;

// Breakpoints (for responsive design)
export const breakpoints = {
  xs: '0px',
  sm: '600px',
  md: '900px',
  lg: '1200px',
  xl: '1536px',
} as const;

// Z-Index Scale
export const zIndex = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
} as const;

// Animation/Transition
export const animation = {
  duration: {
    fast: '150ms',
    normal: '200ms',
    slow: '300ms',
    slower: '500ms',
  },
  
  easing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  },
} as const;

// Common CSS-in-JS styles
export const commonStyles = {
  // Flexbox utilities
  flexCenter: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  flexBetween: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  // Text utilities
  textEllipsis: {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap' as const,
  },
  
  // Focus styles
  focusRing: {
    outline: 'none',
    boxShadow: `0 0 0 3px rgba(59, 130, 246, 0.1)`,
    borderColor: colors.accent.primary,
  },
  
  // Scrollbar styles
  customScrollbar: {
    '&::-webkit-scrollbar': {
      width: '6px',
      height: '6px',
    },
    '&::-webkit-scrollbar-track': {
      background: colors.neutral[100],
      borderRadius: borderRadius.full,
    },
    '&::-webkit-scrollbar-thumb': {
      background: colors.neutral[300],
      borderRadius: borderRadius.full,
      '&:hover': {
        background: colors.neutral[400],
      },
    },
  },
} as const;

// Export everything as default for convenience
export default {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  breakpoints,
  zIndex,
  animation,
  commonStyles,
};
