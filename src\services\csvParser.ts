// src/services/csvParser.ts

export interface ParsedData {
    x: any[];
    y: any[];
    chartType: string;
    library: string;
  }
  
  export const parseCSV = (csvText: string): ParsedData => {
    const rows = csvText.split("\n").map(row => row.trim()).filter(row => row.length > 0);
    const headers = rows[0].split(",").map(header => header.trim()); // Assuming first row contains headers
    
    // Handle data parsing for x and y columns
    const xIndex = headers.indexOf("x");
    const yIndex = headers.indexOf("y");
  
    if (xIndex === -1 || yIndex === -1) {
      throw new Error("CSV does not contain required 'x' and 'y' columns.");
    }
  
    const data = rows.slice(1).map(row => {
      const columns = row.split(",").map(col => col.trim());
      return {
        x: columns[xIndex],
        y: parseFloat(columns[yIndex]),
      };
    });
  
    return {
      x: data.map(d => d.x),
      y: data.map(d => d.y),
      chartType: "scatter", // Default chart type
      library: "plotly", // Default library
    };
  };
  
  export const parseCSVFile = async (file: File): Promise<ParsedData> => {
    const text = await file.text();
    return parseCSV(text);
  };
  