import React, { useEffect, useState } from 'react';
import { Box, Typography, CircularProgress, Snackbar } from '@mui/material';
import FileUpload from './FileUpload';
import ErrorDisplay from './common/ErrorDisplay';
import TimeoutErrorDisplay from './common/TimeoutErrorDisplay';
import { ApiError } from '../services/apiService';

// Utility function for smooth scroll to top
const scrollToTop = (smooth: boolean = true) => {
  // First try to scroll the main content area
  const mainContentArea = document.querySelector('[data-main-content="true"]');
  if (mainContentArea) {
    mainContentArea.scrollTo({
      top: 0,
      left: 0,
      behavior: smooth ? 'smooth' : 'auto'
    });
  }
  // Also scroll window to top as fallback
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: smooth ? 'smooth' : 'auto'
  });
};

// Step-by-step loading states for a more engaging experience
const LOADING_STEPS = [
  'Reading Data',
  'Identifying Data Points',
  'Aggregating Data',
  'Identifying Suitable Charts',
  'Preparing Charts'
];

interface DataPageProps {
  onDataProcessed: (data: { charts: any[], available_columns: string[] }) => void;
  onError?: (error: ApiError) => void;
  onUploadStart?: () => void; // New callback to trigger progress steps immediately
  isLoading: boolean;
  loadingStep: number;
  showSteps: boolean;
}

const DataPage: React.FC<DataPageProps> = ({
  onDataProcessed,
  onError,
  onUploadStart,
  isLoading,
  loadingStep,
  showSteps
}) => {
  const [localError, setLocalError] = useState<ApiError | null>(null);

  // Scroll to top when component mounts
  useEffect(() => {
    scrollToTop();
  }, []);

  const clearError = () => setLocalError(null);

  const handleError = (error: ApiError) => {
    setLocalError(error);
    onError?.(error);
  };

  return (
    <Box sx={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',

      p: 3,
      minHeight: '100vh',
      backgroundColor: 'white',
    }}>
      {/* Page Title */}
      <Typography
        variant="h4"
        sx={{
          mb: 2,
          fontWeight: 'bold',
          color: 'text.primary'
        }}
      >
        Your Data
      </Typography>

    

      {/* Loading Section */}
      {isLoading ? (
        <Box sx={{
          width: '100%',
          maxWidth: '500px',
          minHeight: '400px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'white',
          borderRadius: '16px',
          p: 4,
          boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
          border: '1px solid rgba(0,0,0,0.1)',
          transition: 'transform 0.4s ease, box-shadow 0.4s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
          },
        }}>
          <CircularProgress size={60} sx={{ mb: 3 }} />
          <Typography variant="h6" sx={{ mb: 3, textAlign: 'center' }}>
            Processing your data...
          </Typography>
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
            width: '100%',
          }}>
            {showSteps && LOADING_STEPS.map((step, idx) => (
              <Typography
                key={step}
                variant="body1"
                color={idx === loadingStep ? 'primary.main' : 'text.secondary'}
                sx={{
                  fontWeight: idx === loadingStep ? 600 : 400,
                  opacity: idx <= loadingStep ? 1 : 0.5,
                  transition: 'color 0.3s, opacity 0.3s',
                  textAlign: 'center',
                }}
              >
                {idx === loadingStep && '⏳ '}{step}
                {idx < loadingStep && ' ✓'}
              </Typography>
            ))}
          </Box>
        </Box>
      ) : (
        /* File Upload Section */
        <Box sx={{
          width: '100%',
          maxWidth: { xs: '100%', sm: '450px', md: '500px' },
          height: { xs: '350px', sm: '380px', md: '400px' },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
          borderRadius: { xs: '12px', md: '16px' },
          transition: 'transform 0.4s ease, box-shadow 0.4s ease',
          mx: { xs: 2, sm: 0 },
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
          },
        }}>
          <FileUpload
            onDataProcessed={(data: any[]) => onDataProcessed({ charts: data, available_columns: [] })}
            onError={handleError}
            onUploadStart={onUploadStart}
          />
        </Box>
      )}

      {/* Error Snackbar */}
      <Snackbar
        open={!!localError}
        autoHideDuration={8000} // Longer duration for timeout errors
        onClose={clearError}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        {localError && (localError.message.toLowerCase().includes('timeout') ||
                       localError.message.toLowerCase().includes('timed out')) ? (
          <TimeoutErrorDisplay
            message={localError.message}
            operationType="upload"
            variant="modal"
            onRetry={clearError}
          />
        ) : (
          <ErrorDisplay
            error={localError || 'An error occurred'}
            variant="snackbar"
          />
        )}
      </Snackbar>
    </Box>
  );
};

export default DataPage;
