import React from 'react';
import { <PERSON>, Typo<PERSON>, <PERSON>ton, Alert, Paper } from '@mui/material';

const TestPage: React.FC = () => {

  const handleTestError = () => {
    throw new Error('Test error for global error handler');
  };

  const handleTestPromiseRejection = () => {
    Promise.reject(new Error('Test promise rejection'));
  };

  return (
    <Box sx={{ p: 4, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        🧪 Test Page - Verify Fixes
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          ✅ React Hook Issues Fixed
        </Typography>
        <Typography variant="body2" color="text.secondary">
          If you can see this page without console errors, the React hook issues are resolved.
        </Typography>
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          🔄 Auto-Refresh Disabled
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Version checking and auto-refresh functionality has been removed to eliminate WebSocket issues.
        </Typography>
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          🚨 Error Handling Test
        </Typography>
        <Typography variant="body2" gutterBottom>
          Test the global error handler (check browser console):
        </Typography>
        <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
          <Button 
            variant="outlined" 
            color="warning"
            onClick={handleTestError}
          >
            Test Sync Error
          </Button>
          <Button 
            variant="outlined" 
            color="warning"
            onClick={handleTestPromiseRejection}
          >
            Test Promise Rejection
          </Button>
        </Box>
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          🌐 WebSocket Status
        </Typography>
        <Typography variant="body2">
          If you see this page loading without WebSocket errors in the console, 
          the Vite HMR issues are resolved.
        </Typography>
      </Paper>

      <Alert severity="success">
        <Typography variant="body2">
          <strong>All systems operational!</strong> If you can see this page without errors, 
          all the major issues have been resolved:
        </Typography>
        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
          <li>React hook calls are working properly</li>
          <li>No duplicate React/Emotion instances</li>
          <li>WebSocket connections disabled (no more errors)</li>
          <li>Auto-refresh functionality removed</li>
          <li>Global error handling is functional</li>
        </ul>
      </Alert>
    </Box>
  );
};

export default TestPage;
