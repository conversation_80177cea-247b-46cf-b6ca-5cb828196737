import React, { useEffect, useCallback } from 'react';
import { TimeoutError } from '../services/apiService';

interface GlobalErrorHandlerOptions {
  onError?: (error: Error, context: string) => void;
  enableConsoleLogging?: boolean;
  enableErrorReporting?: boolean;
}

/**
 * Hook to handle global errors including unhandled promise rejections,
 * timeout errors, and other uncaught exceptions
 */
export const useGlobalErrorHandler = (options: GlobalErrorHandlerOptions = {}) => {
  const {
    onError,
    enableConsoleLogging = true,
    enableErrorReporting = false
  } = options;

  const logError = useCallback((error: Error, context: string) => {
    if (enableConsoleLogging) {
      console.error(`[GlobalErrorHandler] ${context}:`, {
        message: error.message,
        stack: error.stack,
        name: error.name,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      });
    }

    // Call custom error handler if provided
    if (onError) {
      onError(error, context);
    }

    // Report to external service if enabled
    if (enableErrorReporting) {
      reportErrorToService(error, context);
    }
  }, [onError, enableConsoleLogging, enableErrorReporting]);

  const reportErrorToService = useCallback((error: Error, context: string) => {
    // This would integrate with services like Sentry, LogRocket, etc.
    // For now, we'll just log it
    console.info('[GlobalErrorHandler] Would report to external service:', {
      error: error.message,
      context,
      timestamp: new Date().toISOString()
    });
  }, []);

  const handleUnhandledRejection = useCallback((event: PromiseRejectionEvent) => {
    const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
    
    // Check if it's a timeout error
    if (error instanceof TimeoutError || 
        error.message.toLowerCase().includes('timeout') ||
        error.message.toLowerCase().includes('timed out')) {
      logError(error, 'Unhandled Promise Rejection (Timeout)');
    } else {
      logError(error, 'Unhandled Promise Rejection');
    }

    // Prevent the default browser behavior (logging to console)
    event.preventDefault();
  }, [logError]);

  const handleError = useCallback((event: ErrorEvent) => {
    const error = event.error instanceof Error ? event.error : new Error(event.message);
    logError(error, 'Uncaught Exception');
  }, [logError]);

  const handleResourceError = useCallback((event: Event) => {
    const target = event.target as HTMLElement;
    const error = new Error(`Resource failed to load: ${target.tagName} ${target.getAttribute('src') || target.getAttribute('href') || 'unknown'}`);
    logError(error, 'Resource Load Error');
  }, [logError]);

  useEffect(() => {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    // Handle uncaught exceptions
    window.addEventListener('error', handleError);
    
    // Handle resource loading errors (images, scripts, etc.)
    window.addEventListener('error', handleResourceError, true);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
      window.removeEventListener('error', handleResourceError, true);
    };
  }, [handleUnhandledRejection, handleError, handleResourceError]);

  // Return utility functions for manual error reporting
  return {
    reportError: logError,
    reportTimeoutError: (error: TimeoutError, context: string = 'Manual Report') => {
      logError(error, `${context} (Timeout)`);
    },
    reportNetworkError: (error: Error, context: string = 'Network Error') => {
      logError(error, context);
    }
  };
};

/**
 * Utility function to check if an error is likely a timeout error
 */
export const isTimeoutError = (error: any): boolean => {
  if (error instanceof TimeoutError) {
    return true;
  }
  
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    return message.includes('timeout') ||
           message.includes('timed out') ||
           message.includes('request took too long') ||
           message.includes('network timeout') ||
           error.name === 'TimeoutError';
  }
  
  if (typeof error === 'string') {
    const message = error.toLowerCase();
    return message.includes('timeout') ||
           message.includes('timed out');
  }
  
  return false;
};

/**
 * Utility function to check if an error is likely a network error
 */
export const isNetworkError = (error: any): boolean => {
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    return message.includes('network') ||
           message.includes('fetch') ||
           message.includes('connection') ||
           message.includes('offline') ||
           error.name === 'NetworkError';
  }
  
  if (typeof error === 'string') {
    const message = error.toLowerCase();
    return message.includes('network') ||
           message.includes('connection') ||
           message.includes('offline');
  }
  
  return false;
};

/**
 * Utility function to get a user-friendly error message
 */
export const getUserFriendlyErrorMessage = (error: any): string => {
  if (isTimeoutError(error)) {
    return 'The request is taking longer than expected. Please check your internet connection and try again.';
  }
  
  if (isNetworkError(error)) {
    return 'Unable to connect to our servers. Please check your internet connection and try again.';
  }
  
  if (error instanceof Error) {
    // Check for specific error patterns
    const message = error.message.toLowerCase();
    
    if (message.includes('429') || message.includes('rate limit')) {
      return 'Too many requests. Please wait a moment and try again.';
    }
    
    if (message.includes('500') || message.includes('server error')) {
      return 'Our servers are experiencing issues. Please try again later.';
    }
    
    if (message.includes('401') || message.includes('unauthorized')) {
      return 'Your session has expired. Please sign in again.';
    }
    
    if (message.includes('403') || message.includes('forbidden')) {
      return 'You do not have permission to perform this action.';
    }
    
    if (message.includes('404') || message.includes('not found')) {
      return 'The requested resource was not found.';
    }
  }
  
  return 'An unexpected error occurred. Please try again.';
};

export default useGlobalErrorHandler;
