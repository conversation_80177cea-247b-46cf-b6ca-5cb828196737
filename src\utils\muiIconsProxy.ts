// Proxy file to handle MUI icons without loading the entire library
// This prevents EMFILE errors by providing a single entry point

import React from 'react';

// Generic icon component that can represent any MUI icon
const GenericIcon: React.FC<any> = (props) => {
  return React.createElement('span', {
    ...props,
    style: {
      display: 'inline-block',
      width: '24px',
      height: '24px',
      fontSize: '24px',
      ...props.style
    }
  }, props.children || '📊');
};

// Export commonly used icons as the generic component
export const ArrowBackIcon = GenericIcon;
export const ContentCopyIcon = GenericIcon;
export const DownloadIcon = GenericIcon;
export const CloudUploadIcon = GenericIcon;
export const BarChartIcon = GenericIcon;
export const PreviewIcon = GenericIcon;
export const EditIcon = GenericIcon;
export const InsightsIcon = GenericIcon;
export const CloseIcon = GenericIcon;
export const ExpandMoreIcon = GenericIcon;
export const RestoreIcon = GenericIcon;
export const DatasetIcon = GenericIcon;
export const LaunchIcon = GenericIcon;
export const UploadFileIcon = GenericIcon;

// Default export
export default GenericIcon;
