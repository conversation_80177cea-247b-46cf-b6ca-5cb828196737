// Proxy file to handle MUI icons without loading the entire library
// This prevents EMFILE errors by providing a single entry point

import React from 'react';

// Create specific icon components with appropriate symbols
const createIcon = (symbol: string) => {
  const IconComponent: React.FC<any> = (props) => {
    return React.createElement('span', {
      ...props,
      style: {
        display: 'inline-block',
        width: '24px',
        height: '24px',
        fontSize: '20px',
        lineHeight: '24px',
        textAlign: 'center',
        ...props.style
      }
    }, symbol);
  };
  return IconComponent;
};

// Export all icons used in the application with specific symbols
export const ArrowBackIcon = createIcon('←');
export const ContentCopyIcon = createIcon('📋');
export const DownloadIcon = createIcon('⬇️');
export const CloudUploadIcon = createIcon('☁️');
export const BarChartIcon = createIcon('📊');
export const PreviewIcon = createIcon('👁️');
export const EditIcon = createIcon('✏️');
export const InsightsIcon = createIcon('💡');
export const CloseIcon = createIcon('✕');
export const ExpandMoreIcon = createIcon('⌄');
export const RestoreIcon = createIcon('↻');
export const DatasetIcon = createIcon('📄');
export const LaunchIcon = createIcon('🚀');
export const UploadFileIcon = createIcon('📁');

// Additional icons from ErrorBoundary and other components
export const ErrorOutlineIcon = createIcon('⚠️');
export const RefreshIcon = createIcon('↻');
export const HomeIcon = createIcon('🏠');
export const VisibilityIcon = createIcon('👁️');
export const CircleIcon = createIcon('●');
export const MoreHorizIcon = createIcon('⋯');
export const WarningAmberIcon = createIcon('⚠️');
export const AccessTimeIcon = createIcon('⏰');
export const WifiOffIcon = createIcon('📶');
export const InfoIcon = createIcon('ℹ️');
export const TipsAndUpdatesIcon = createIcon('💡');
export const NetworkCheckIcon = createIcon('🌐');
export const ImageIcon = createIcon('🖼️');
export const TextFieldsIcon = createIcon('📝');
export const StartIcon = createIcon('🚀');
export const PresentationIcon = createIcon('📊');
export const ArticleIcon = createIcon('📄');
export const ShareIcon = createIcon('📤');
export const DescriptionIcon = createIcon('📋');
export const BusinessIcon = createIcon('🏢');
export const MenuIcon = createIcon('☰');

// Additional icons that might be used
export const SlideshowIcon = createIcon('📊'); // For PresentationIcon alias
export const LightbulbIcon = createIcon('💡'); // For InsightsIcon in Chart.tsx

// Named exports for ErrorModal destructured imports
export const Warning = createIcon('⚠️');
export const Close = createIcon('✕');
export const ExpandMore = createIcon('⌄');
export const Refresh = createIcon('↻');
export const ArrowBack = createIcon('←');
export const Info = createIcon('ℹ️');

// Additional named exports for Sidebar
export const Menu = createIcon('☰');

// Default export - fallback generic icon
const GenericIcon = createIcon('📊');
export default GenericIcon;
