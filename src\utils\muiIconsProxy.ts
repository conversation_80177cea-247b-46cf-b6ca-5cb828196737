// Proxy file to handle MUI icons without loading the entire library
// This prevents EMFILE errors by providing a single entry point

import React from 'react';

// Generic icon component that can represent any MUI icon
const GenericIcon: React.FC<any> = (props) => {
  return React.createElement('span', {
    ...props,
    style: {
      display: 'inline-block',
      width: '24px',
      height: '24px',
      fontSize: '24px',
      ...props.style
    }
  }, props.children || '📊');
};

// Export all icons used in the application as the generic component
export const ArrowBackIcon = GenericIcon;
export const ContentCopyIcon = GenericIcon;
export const DownloadIcon = GenericIcon;
export const CloudUploadIcon = GenericIcon;
export const BarChartIcon = GenericIcon;
export const PreviewIcon = GenericIcon;
export const EditIcon = GenericIcon;
export const InsightsIcon = GenericIcon;
export const CloseIcon = GenericIcon;
export const ExpandMoreIcon = GenericIcon;
export const RestoreIcon = GenericIcon;
export const DatasetIcon = GenericIcon;
export const LaunchIcon = GenericIcon;
export const UploadFileIcon = GenericIcon;

// Additional icons from ErrorBoundary and other components
export const ErrorOutlineIcon = GenericIcon;
export const RefreshIcon = GenericIcon;
export const HomeIcon = GenericIcon;
export const VisibilityIcon = GenericIcon;
export const CircleIcon = GenericIcon;
export const MoreHorizIcon = GenericIcon;
export const WarningAmberIcon = GenericIcon;
export const AccessTimeIcon = GenericIcon;
export const WifiOffIcon = GenericIcon;
export const InfoIcon = GenericIcon;
export const TipsAndUpdatesIcon = GenericIcon;
export const NetworkCheckIcon = GenericIcon;
export const ImageIcon = GenericIcon;
export const TextFieldsIcon = GenericIcon;
export const StartIcon = GenericIcon;
export const PresentationIcon = GenericIcon;
export const ArticleIcon = GenericIcon;
export const ShareIcon = GenericIcon;
export const DescriptionIcon = GenericIcon;
export const BusinessIcon = GenericIcon;
export const MenuIcon = GenericIcon;

// Additional icons that might be used
export const SlideshowIcon = GenericIcon; // For PresentationIcon alias
export const LightbulbIcon = GenericIcon; // For InsightsIcon in Chart.tsx

// Named exports for ErrorModal destructured imports
export const Warning = GenericIcon;
export const Close = GenericIcon;
export const ExpandMore = GenericIcon;
export const Refresh = GenericIcon;
export const ArrowBack = GenericIcon;
export const Info = GenericIcon;

// Additional named exports for Sidebar
export const Menu = GenericIcon;

// Default export
export default GenericIcon;
