import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON>po<PERSON>, 
  <PERSON>ert, 
  <PERSON><PERSON><PERSON><PERSON>le, 
  <PERSON>, 
  Accordion, 
  AccordionSummary, 
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import InfoIcon from '@mui/icons-material/Info';
import TipsAndUpdatesIcon from '@mui/icons-material/TipsAndUpdates';
import NetworkCheckIcon from '@mui/icons-material/NetworkCheck';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import InsightsIcon from '@mui/icons-material/Insights';
import EditIcon from '@mui/icons-material/Edit';
import config from '../../config';

interface TimeoutInfoProps {
  variant?: 'compact' | 'detailed';
  showConfiguration?: boolean;
  operationType?: 'upload' | 'insights' | 'chart_update' | 'general';
}

const TimeoutInfo: React.FC<TimeoutInfoProps> = ({
  variant = 'compact',
  showConfiguration = true,
  operationType = 'general'
}) => {
  const [expanded, setExpanded] = useState(false);

  const timeoutSeconds = config.apiTimeout / 1000;

  const getOperationIcon = () => {
    switch (operationType) {
      case 'upload': return <CloudUploadIcon />;
      case 'insights': return <InsightsIcon />;
      case 'chart_update': return <EditIcon />;
      default: return <AccessTimeIcon />;
    }
  };

  const getOperationDescription = () => {
    switch (operationType) {
      case 'upload':
        return 'File uploads automatically timeout to prevent hanging uploads. Large files may require more time.';
      case 'insights':
        return 'Insight generation timeouts prevent long-running analysis from blocking the interface.';
      case 'chart_update':
        return 'Chart updates timeout to ensure responsive user interactions.';
      default:
        return 'API requests timeout to prevent hanging requests and maintain application responsiveness.';
    }
  };

  const getTroubleshootingTips = () => {
    const commonTips = [
      'Check your internet connection speed',
      'Try again during off-peak hours',
      'Ensure you have a stable network connection'
    ];

    const operationSpecificTips = {
      upload: [
        'Try uploading smaller files (under 10MB)',
        'Ensure your file is in a supported format (CSV, Excel)',
        'Remove unnecessary columns or rows from your data',
        'Check if your file has any formatting issues'
      ],
      insights: [
        'Complex data may require more processing time',
        'Try simplifying your data if possible',
        'Wait a few moments before retrying',
        'Consider breaking large datasets into smaller chunks'
      ],
      chart_update: [
        'Try refreshing the page',
        'Clear your browser cache',
        'Ensure you have the latest version of the application'
      ],
      general: [
        'Refresh the page and try again',
        'Clear your browser cache and cookies',
        'Try using a different browser'
      ]
    };

    return [...commonTips, ...operationSpecificTips[operationType]];
  };

  if (variant === 'compact') {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <AccessTimeIcon fontSize="small" />
          <Typography variant="body2" sx={{ fontWeight: 600 }}>
            Timeout Configuration
          </Typography>
          <Chip 
            label={`${timeoutSeconds}s`} 
            size="small" 
            color="primary" 
          />
        </Box>
        <Typography variant="body2">
          {getOperationDescription()}
        </Typography>
      </Alert>
    );
  }

  return (
    <Box sx={{ mb: 3 }}>
      <Alert severity="info" sx={{ mb: 2 }}>
        <AlertTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {getOperationIcon()}
          Timeout Information
        </AlertTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Typography variant="body2">
            Current timeout setting:
          </Typography>
          <Chip 
            label={`${timeoutSeconds} seconds`} 
            size="small" 
            color="primary" 
          />
        </Box>
        <Typography variant="body2">
          {getOperationDescription()}
        </Typography>
      </Alert>

      {showConfiguration && (
        <Accordion 
          expanded={expanded} 
          onChange={() => setExpanded(!expanded)}
          sx={{ mb: 2 }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TipsAndUpdatesIcon color="primary" />
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                Troubleshooting Tips
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
              If you're experiencing timeout issues, try these solutions:
            </Typography>
            
            <List dense>
              {getTroubleshootingTips().map((tip, index) => (
                <ListItem key={index} sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <Box sx={{ 
                      width: 6, 
                      height: 6, 
                      borderRadius: '50%', 
                      backgroundColor: 'primary.main' 
                    }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary={tip} 
                    primaryTypographyProps={{ variant: 'body2' }}
                  />
                </ListItem>
              ))}
            </List>

            <Divider sx={{ my: 2 }} />

            <Alert severity="warning" sx={{ mt: 2 }}>
              <AlertTitle sx={{ fontSize: '0.875rem' }}>
                Network Status
              </AlertTitle>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <NetworkCheckIcon fontSize="small" />
                <Typography variant="body2">
                  Connection: {navigator.onLine ? 'Online' : 'Offline'}
                </Typography>
              </Box>
              {!navigator.onLine && (
                <Typography variant="body2" sx={{ mt: 1, color: 'error.main' }}>
                  You appear to be offline. Please check your internet connection.
                </Typography>
              )}
            </Alert>

            <Alert severity="info" sx={{ mt: 2 }}>
              <AlertTitle sx={{ fontSize: '0.875rem' }}>
                Technical Details
              </AlertTitle>
              <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
                Timeout: {config.apiTimeout}ms<br />
                User Agent: {navigator.userAgent.substring(0, 50)}...<br />
                Timestamp: {new Date().toISOString()}
              </Typography>
            </Alert>
          </AccordionDetails>
        </Accordion>
      )}
    </Box>
  );
};

export default TimeoutInfo;
