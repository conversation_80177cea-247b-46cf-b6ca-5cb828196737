import React, { useRef } from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText,
  Fade
} from '@mui/material';
import CircleIcon from '@mui/icons-material/Circle';
import Plot from 'react-plotly.js';

// Insight Card Component
interface InsightCardProps {
  title: string;
  icon: React.ReactNode;
  insights: string[];
  color: string;
  gridArea?: string;
}

export const InsightCard: React.FC<InsightCardProps> = ({ 
  title, 
  icon, 
  insights, 
  color, 
  gridArea 
}) => {
  return (
    <Fade in timeout={800}>
      <Paper
        elevation={8}
        sx={{
          gridArea,
          p: 3,
          borderRadius: 3,
          background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
          border: `2px solid ${color}30`,
          height: '100%',
          overflow: 'auto',
          position: 'relative',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: `0 20px 40px ${color}20`,
            transition: 'all 0.3s ease'
          }
        }}
      >
        {/* Header */}
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          mb: 2,
          pb: 2,
          borderBottom: `2px solid ${color}30`
        }}>
          <Box sx={{ 
            color, 
            mr: 2,
            p: 1,
            borderRadius: '50%',
            backgroundColor: `${color}15`
          }}>
            {icon}
          </Box>
          <Typography 
            variant="h6" 
            sx={{ 
              fontWeight: 'bold',
              color: color,
              textShadow: '0 1px 2px rgba(0,0,0,0.1)'
            }}
          >
            {title}
          </Typography>
        </Box>

        {/* Insights List */}
        <List dense sx={{ p: 0 }}>
          {insights.map((insight, index) => (
            <Fade key={index} in timeout={1000 + index * 200}>
              <ListItem 
                sx={{ 
                  px: 0,
                  py: 1,
                  '&:hover': {
                    backgroundColor: `${color}08`,
                    borderRadius: 1
                  }
                }}
              >
                <ListItemIcon sx={{ minWidth: '24px' }}>
                  <CircleIcon sx={{ fontSize: 8, color }} />
                </ListItemIcon>
                <ListItemText 
                  primary={insight}
                  primaryTypographyProps={{
                    variant: 'body2',
                    sx: { 
                      lineHeight: 1.4,
                      color: '#2c3e50'
                    }
                  }}
                />
              </ListItem>
            </Fade>
          ))}
        </List>

        {/* Decorative corner */}
        <Box sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: 60,
          height: 60,
          background: `linear-gradient(135deg, ${color}20 0%, transparent 70%)`,
          borderBottomLeftRadius: '100%'
        }} />
      </Paper>
    </Fade>
  );
};

// Chart Display Component
interface ChartDisplayProps {
  chartData: any;
  gridArea?: string;
}

export const ChartDisplay: React.FC<ChartDisplayProps> = ({ chartData, gridArea }) => {
  const plotRef = useRef<any>(null);

  // Prepare chart data for display
  const prepareData = () => {
    const { data: rawData, chart_type } = chartData;
    let plotData = Array.isArray(rawData) ? rawData : [rawData];

    // Handle different chart types
    if (chart_type === 'pie' || chart_type === 'donut') {
      return plotData.map(trace => ({
        ...trace,
        type: 'pie',
        labels: trace.labels || trace.x,
        values: trace.values || trace.y,
        hole: chart_type === 'donut' ? 0.4 : 0,
        textinfo: 'percent', // Show only percentages, categories shown in legend
        textposition: 'auto',
        marker: {
          line: { width: 2, color: 'white' }
        }
      }));
    }

    if (chart_type === 'bar') {
      return plotData.map(trace => ({
        ...trace,
        type: 'bar',
        text: trace.y || trace.values,
        textposition: 'outside',
        marker: {
          color: '#3498db',
          line: { width: 1, color: 'white' }
        }
      }));
    }

    if (chart_type === 'line') {
      return plotData.map(trace => ({
        ...trace,
        type: 'scatter',
        mode: 'lines+markers',
        line: { width: 3, color: '#e74c3c' },
        marker: { size: 8, color: '#e74c3c' }
      }));
    }

    return plotData;
  };

  const getLayout = () => ({
    title: {
      text: chartData.layout?.title || 'Chart',
      font: { size: 18, color: '#2c3e50', family: 'Arial, sans-serif' },
      x: 0.5
    },
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
    margin: { t: 60, r: 40, b: 60, l: 60 },
    font: { color: '#2c3e50', family: 'Arial, sans-serif' },
    xaxis: {
      title: chartData.layout?.xaxis?.title || '',
      gridcolor: '#ecf0f1',
      linecolor: '#bdc3c7'
    },
    yaxis: {
      title: chartData.layout?.yaxis?.title || '',
      gridcolor: '#ecf0f1',
      linecolor: '#bdc3c7'
    },
    showlegend: false
  });

  return (
    <Fade in timeout={600}>
      <Paper
        elevation={12}
        sx={{
          gridArea,
          p: 2,
          borderRadius: 3,
          background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
          border: '3px solid #e9ecef',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          overflow: 'hidden',
          '&:hover': {
            transform: 'scale(1.02)',
            boxShadow: '0 25px 50px rgba(0,0,0,0.15)',
            transition: 'all 0.3s ease'
          }
        }}
      >
        {/* Chart Container */}
        <Box sx={{ 
          flexGrow: 1, 
          position: 'relative',
          minHeight: 300
        }}>
          <Plot
            ref={plotRef}
            data={prepareData()}
            layout={getLayout()}
            useResizeHandler={true}
            style={{ width: '100%', height: '100%' }}
            config={{
              responsive: true,
              displayModeBar: false,
              displaylogo: false,
            }}
          />
        </Box>

        {/* Decorative elements */}
        <Box sx={{
          position: 'absolute',
          top: -20,
          left: -20,
          width: 80,
          height: 80,
          background: 'linear-gradient(135deg, #3498db20 0%, transparent 70%)',
          borderRadius: '50%'
        }} />
        
        <Box sx={{
          position: 'absolute',
          bottom: -20,
          right: -20,
          width: 80,
          height: 80,
          background: 'linear-gradient(135deg, #e74c3c20 0%, transparent 70%)',
          borderRadius: '50%'
        }} />
      </Paper>
    </Fade>
  );
};
