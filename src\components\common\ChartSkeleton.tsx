import React from 'react';
import { Box, Paper, Skeleton } from '@mui/material';

interface ChartSkeletonProps {
  width?: string;
  height?: string;
  aspectRatio?: string;
}

const ChartSkeleton: React.FC<ChartSkeletonProps> = ({
  width = '100%', // Changed to full width for responsive grid
  height,
  aspectRatio = '1.5/1'
}) => {
  return (
    <Paper
      elevation={0}
      sx={{
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: 2,
        overflow: 'hidden',
        width,
        height,
        aspectRatio: height ? undefined : aspectRatio,
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
        p: 2,
      }}
    >
      {/* Chart Title Skeleton */}
      <Box sx={{ mb: 2 }}>
        <Skeleton 
          variant="text" 
          width="60%" 
          height={24}
          sx={{ fontSize: '1.2rem' }}
        />
      </Box>

      {/* Chart Content Skeleton */}
      <Box sx={{ 
        flexGrow: 1, 
        display: 'flex', 
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 2
      }}>
        {/* Main chart area */}
        <Skeleton 
          variant="rectangular" 
          width="100%" 
          height="70%"
          sx={{ borderRadius: 1 }}
        />
        
        {/* Legend/axis labels */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          width: '100%',
          gap: 1
        }}>
          <Skeleton variant="text" width="20%" height={16} />
          <Skeleton variant="text" width="25%" height={16} />
          <Skeleton variant="text" width="18%" height={16} />
        </Box>
      </Box>

      {/* Action buttons skeleton */}
      <Box sx={{
        position: 'absolute',
        top: 10,
        right: 10,
        display: 'flex',
        gap: 1,
      }}>
        <Skeleton variant="circular" width={32} height={32} />
        <Skeleton variant="circular" width={32} height={32} />
      </Box>
    </Paper>
  );
};

export default ChartSkeleton;
