import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import ErrorModal from '../components/common/ErrorModal';
import { ErrorModalProps } from '../components/common/ErrorModal';
import { createErrorModalProps } from '../utils/errorModalUtils';

interface ErrorModalContextType {
  showError: (
    error: any,
    options?: {
      onRetry?: () => void;
      onBack?: () => void;
      retryLabel?: string;
      backLabel?: string;
      customTitle?: string;
      customDescription?: string;
      context?: string;
      enableBackNavigation?: boolean;
    }
  ) => void;
  showCustomError: (props: Partial<ErrorModalProps>) => void;
  closeError: () => void;
  isErrorOpen: boolean;
}

const ErrorModalContext = createContext<ErrorModalContextType | undefined>(undefined);

interface ErrorModalProviderProps {
  children: ReactNode;
  onNavigate?: (page: string) => void;
}

export const ErrorModalProvider: React.FC<ErrorModalProviderProps> = ({ 
  children, 
  onNavigate 
}) => {
  const [modalProps, setModalProps] = useState<ErrorModalProps>({
    open: false,
    onClose: () => {},
    title: '',
    description: ''
  });

  const closeError = useCallback(() => {
    setModalProps(prev => ({ ...prev, open: false }));
  }, []);

  const showError = useCallback((
    error: any,
    options: {
      onRetry?: () => void;
      onBack?: () => void;
      retryLabel?: string;
      backLabel?: string;
      customTitle?: string;
      customDescription?: string;
      context?: string;
      enableBackNavigation?: boolean;
    } = {}
  ) => {
    const {
      enableBackNavigation = true,
      onBack,
      ...restOptions
    } = options;

    // Default back handler that goes to previous page
    const defaultBackHandler = enableBackNavigation 
      ? () => {
          if (window.history.length > 1) {
            window.history.back();
          } else if (onNavigate) {
            onNavigate('home');
          } else {
            window.location.reload();
          }
        }
      : undefined;

    const finalOptions = {
      ...restOptions,
      onBack: onBack || defaultBackHandler
    };

    const props = createErrorModalProps(error, finalOptions);
    setModalProps({
      ...props,
      open: true,
      onClose: closeError
    });
  }, [onNavigate, closeError]);

  const showCustomError = useCallback((props: Partial<ErrorModalProps>) => {
    setModalProps(prev => ({
      ...prev,
      ...props,
      open: true,
      onClose: closeError
    }));
  }, [closeError]);

  const contextValue: ErrorModalContextType = {
    showError,
    showCustomError,
    closeError,
    isErrorOpen: modalProps.open
  };

  return (
    <ErrorModalContext.Provider value={contextValue}>
      {children}
      {/* Single ErrorModal instance for the entire app */}
      <ErrorModal {...modalProps} />
    </ErrorModalContext.Provider>
  );
};

export const useErrorModal = (): ErrorModalContextType => {
  const context = useContext(ErrorModalContext);
  if (context === undefined) {
    throw new Error('useErrorModal must be used within an ErrorModalProvider');
  }
  return context;
};

// API-specific error handling hook
export const useApiErrorModal = () => {
  const errorModal = useErrorModal();

  const handleApiError = useCallback((
    error: any,
    retryFunction?: () => void | Promise<void>,
    context?: string
  ) => {
    const onRetry = retryFunction ? async () => {
      try {
        await retryFunction();
        errorModal.closeError();
      } catch (retryError) {
        // If retry fails, show the new error
        errorModal.showError(retryError, {
          context: `${context} (retry failed)`,
          onRetry: () => handleApiError(retryError, retryFunction, context)
        });
      }
    } : undefined;

    errorModal.showError(error, {
      context,
      onRetry,
      retryLabel: retryFunction ? 'Try Again' : undefined
    });
  }, [errorModal]);

  const handleUploadError = useCallback((
    error: any,
    retryUpload?: () => void | Promise<void>
  ) => {
    handleApiError(error, retryUpload, 'File Upload');
  }, [handleApiError]);

  const handleChartError = useCallback((
    error: any,
    retryChart?: () => void | Promise<void>
  ) => {
    handleApiError(error, retryChart, 'Chart Processing');
  }, [handleApiError]);

  const handleAuthError = useCallback((
    error: any,
    redirectToSignIn?: () => void
  ) => {
    errorModal.showError(error, {
      context: 'Authentication',
      customTitle: 'Authentication Required',
      customDescription: 'Your session has expired. Please sign in again to continue.',
      backLabel: 'Sign In',
      onBack: redirectToSignIn || (() => {
        window.location.href = '/signin';
      }),
      enableBackNavigation: false
    });
  }, [errorModal]);

  return {
    ...errorModal,
    handleApiError,
    handleUploadError,
    handleChartError,
    handleAuthError
  };
};
