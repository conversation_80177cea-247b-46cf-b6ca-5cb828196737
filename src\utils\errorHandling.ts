/**
 * Error handling utilities for API responses and authentication
 */

export interface ApiError {
  status: number;
  message: string;
  code?: string;
  details?: any;
}

export class AuthenticationError extends Error {
  constructor(message: string, public status: number = 401) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class TokenExpiredError extends AuthenticationError {
  constructor(message: string = 'Token has expired') {
    super(message, 401);
    this.name = 'TokenExpiredError';
  }
}

export class RefreshTokenError extends AuthenticationError {
  constructor(message: string = 'Refresh token is invalid or expired') {
    super(message, 401);
    this.name = 'RefreshTokenError';
  }
}

/**
 * Handle API response errors with proper error types
 */
export async function handleApiResponse(response: Response): Promise<any> {
  if (response.ok) {
    return await response.json();
  }

  let errorData: any = {};
  try {
    errorData = await response.json();
  } catch {
    // Response doesn't contain JSON
  }

  const apiError: ApiError = {
    status: response.status,
    message: errorData.message || response.statusText || 'An error occurred',
    code: errorData.code,
    details: errorData.details
  };

  switch (response.status) {
    case 401:
      if (errorData.code === 'TOKEN_EXPIRED' || errorData.message?.includes('expired')) {
        throw new TokenExpiredError(apiError.message);
      }
      if (errorData.code === 'REFRESH_TOKEN_INVALID' || errorData.message?.includes('refresh')) {
        throw new RefreshTokenError(apiError.message);
      }
      throw new AuthenticationError(apiError.message, 401);
    
    case 403:
      throw new Error(`Access forbidden: ${apiError.message}`);
    
    case 404:
      throw new Error(`Resource not found: ${apiError.message}`);
    
    case 429:
      throw new Error(`Rate limit exceeded: ${apiError.message}`);
    
    case 500:
      throw new Error(`Server error: ${apiError.message}`);
    
    default:
      throw new Error(`API Error (${response.status}): ${apiError.message}`);
  }
}

/**
 * Check if an error is authentication-related
 */
export function isAuthError(error: any): boolean {
  return error instanceof AuthenticationError || 
         error instanceof TokenExpiredError || 
         error instanceof RefreshTokenError ||
         (error.status && error.status === 401);
}

/**
 * Check if an error indicates token refresh is needed
 */
export function needsTokenRefresh(error: any): boolean {
  return error instanceof TokenExpiredError ||
         (error.status === 401 && error.code === 'TOKEN_EXPIRED');
}

/**
 * Check if refresh token is invalid and user needs to re-authenticate
 */
export function needsReAuthentication(error: any): boolean {
  return error instanceof RefreshTokenError ||
         (error.status === 401 && error.code === 'REFRESH_TOKEN_INVALID');
}

/**
 * Get user-friendly error message
 */
export function getUserFriendlyErrorMessage(error: any): string {
  if (error instanceof TokenExpiredError) {
    return 'Your session has expired. Please sign in again.';
  }
  
  if (error instanceof RefreshTokenError) {
    return 'Your session is no longer valid. Please sign in again.';
  }
  
  if (error instanceof AuthenticationError) {
    return 'Authentication failed. Please check your credentials and try again.';
  }
  
  if (error.status === 403) {
    return 'You do not have permission to access this resource.';
  }
  
  if (error.status === 404) {
    return 'The requested resource was not found.';
  }
  
  if (error.status === 429) {
    return 'Too many requests. Please wait a moment and try again.';
  }
  
  if (error.status >= 500) {
    return 'A server error occurred. Please try again later.';
  }
  
  return error.message || 'An unexpected error occurred. Please try again.';
}

/**
 * Log error with appropriate level and context
 */
export function logError(error: any, context: string = 'API'): void {
  const errorInfo = {
    context,
    message: error.message,
    status: error.status,
    code: error.code,
    stack: error.stack,
    timestamp: new Date().toISOString()
  };

  if (error.status >= 500) {
    console.error(`[${context}] Server Error:`, errorInfo);
  } else if (error.status === 401) {
    console.warn(`[${context}] Authentication Error:`, errorInfo);
  } else if (error.status >= 400) {
    console.warn(`[${context}] Client Error:`, errorInfo);
  } else {
    console.error(`[${context}] Unexpected Error:`, errorInfo);
  }
}

/**
 * Retry configuration for API calls
 */
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffMultiplier: 2
};

/**
 * Exponential backoff delay calculation
 */
export function calculateBackoffDelay(attempt: number, config: RetryConfig): number {
  const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
  return Math.min(delay, config.maxDelay);
}

/**
 * Check if an error is retryable
 */
export function isRetryableError(error: any): boolean {
  // Don't retry authentication errors
  if (isAuthError(error)) {
    return false;
  }
  
  // Don't retry client errors (4xx except 429)
  if (error.status >= 400 && error.status < 500 && error.status !== 429) {
    return false;
  }
  
  // Retry server errors (5xx) and rate limiting (429)
  return error.status >= 500 || error.status === 429 || !error.status;
}
