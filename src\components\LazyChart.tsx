import React, { Suspense, lazy, useState, useEffect } from 'react';
import { Box } from '@mui/material';
import ChartSkeleton from './common/ChartSkeleton';

// Lazy load the Chart component
const Chart = lazy(() => import('./Chart'));

interface LazyChartProps {
  data: any;
  position?: 'left' | 'center' | 'right';
  onChartUpdate?: (updatedFields: any) => Promise<void>;
  onInsightsRequest?: (chartId: string | number, chartData: any, position: 'left' | 'center' | 'right') => Promise<any>;
  onInsightsClick?: (chartData: any) => void;
  onPreviewClick?: (chartData: any) => void;
  onModifyClick?: (chartData: any) => void;
  width?: string;
  height?: string;
  aspectRatio?: string;
  delay?: number; // Optional delay before loading the chart
  loadImmediately?: boolean; // Whether to load immediately or wait for intersection
}

const LazyChart: React.FC<LazyChartProps> = ({
  data,
  position = 'center',
  onChartUpdate,
  onInsightsRequest,
  onInsightsClick,
  onPreviewClick,
  onModifyClick,
  width = 'calc(50% - 12px)',
  height,
  aspectRatio = '1.5/1',
  delay = 0,
  loadImmediately = false
}) => {
  const [shouldLoad, setShouldLoad] = useState(loadImmediately);
  const [containerRef, setContainerRef] = useState<HTMLDivElement | null>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (loadImmediately || shouldLoad) return;

    if (!containerRef) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Add delay if specified
            if (delay > 0) {
              setTimeout(() => setShouldLoad(true), delay);
            } else {
              setShouldLoad(true);
            }
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin: '50px', // Start loading when chart is 50px away from viewport
        threshold: 0.1
      }
    );

    observer.observe(containerRef);

    return () => {
      if (containerRef) {
        observer.unobserve(containerRef);
      }
    };
  }, [containerRef, delay, loadImmediately, shouldLoad]);

  // If not ready to load, show skeleton
  if (!shouldLoad) {
    return (
      <Box
        ref={setContainerRef}
        sx={{
          width,
          height,
          aspectRatio: height ? undefined : aspectRatio,
        }}
      >
        <ChartSkeleton 
          width="100%" 
          height={height}
          aspectRatio={aspectRatio}
        />
      </Box>
    );
  }

  // Load the actual chart with Suspense
  return (
    <Box
      ref={setContainerRef}
      sx={{
        width,
        height,
        aspectRatio: height ? undefined : aspectRatio,
      }}
    >
      <Suspense 
        fallback={
          <ChartSkeleton 
            width="100%" 
            height={height}
            aspectRatio={aspectRatio}
          />
        }
      >
        <Chart
          data={data}
          position={position}
          onChartUpdate={onChartUpdate}
          onInsightsRequest={onInsightsRequest}
          onInsightsClick={onInsightsClick}
          onPreviewClick={onPreviewClick}
          onModifyClick={onModifyClick}
        />
      </Suspense>
    </Box>
  );
};

export default LazyChart;
