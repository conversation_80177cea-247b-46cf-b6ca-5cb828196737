#!/usr/bin/env node

/**
 * Deployment script that handles version bumping and cache busting
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function updateEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  const packageJsonPath = path.join(process.cwd(), 'package.json');

  log('🔧 Updating environment variables...', 'yellow');
  
  try {
    // Read package.json to get version
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const version = packageJson.version;
    
    // Generate new build hash
    const buildHash = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    const buildTime = new Date().toISOString();
    
    // Read existing .env.local or create new content
    let envContent = '';
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');
    }
    
    // Update or add version variables
    const updates = {
      'VITE_APP_VERSION': version,
      'VITE_BUILD_TIME': buildTime,
      'VITE_BUILD_HASH': buildHash
    };
    
    Object.entries(updates).forEach(([key, value]) => {
      const regex = new RegExp(`^${key}=.*$`, 'm');
      const newLine = `${key}=${value}`;
      
      if (regex.test(envContent)) {
        envContent = envContent.replace(regex, newLine);
      } else {
        envContent += `\n${newLine}`;
      }
    });
    
    // Write updated .env.local
    fs.writeFileSync(envPath, envContent.trim() + '\n');
    
    log(`✅ Updated .env.local with version ${version}`, 'green');
    log(`   Build Hash: ${buildHash}`, 'cyan');
    log(`   Build Time: ${buildTime}`, 'cyan');
    
    return { version, buildHash, buildTime };
  } catch (error) {
    log(`❌ Error updating .env.local: ${error.message}`, 'red');
    throw error;
  }
}

function buildProject() {
  try {
    log('🔨 Building project...', 'yellow');
    execSync('npm run build', { stdio: 'inherit' });
    log('✅ Build completed successfully', 'green');
  } catch (error) {
    log('❌ Build failed', 'red');
    throw error;
  }
}

function generateCacheBustingManifest() {
  const distPath = path.join(process.cwd(), 'dist');
  const manifestPath = path.join(distPath, 'cache-manifest.json');
  
  try {
    // Get all files in dist directory
    const files = [];
    
    function scanDirectory(dir, baseDir = '') {
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const relativePath = path.join(baseDir, item);
        
        if (fs.statSync(fullPath).isDirectory()) {
          scanDirectory(fullPath, relativePath);
        } else {
          files.push({
            path: relativePath.replace(/\\/g, '/'), // Normalize path separators
            size: fs.statSync(fullPath).size,
            modified: fs.statSync(fullPath).mtime.toISOString()
          });
        }
      });
    }
    
    if (fs.existsSync(distPath)) {
      scanDirectory(distPath);
    }
    
    const manifest = {
      version: process.env.VITE_APP_VERSION || '1.0.0',
      buildTime: new Date().toISOString(),
      files: files
    };
    
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
    log(`✅ Generated cache manifest with ${files.length} files`, 'green');
    
  } catch (error) {
    log(`⚠️ Warning: Could not generate cache manifest: ${error.message}`, 'yellow');
  }
}

function main() {
  const args = process.argv.slice(2);
  const bumpVersion = args.includes('--bump') || args.includes('-b');
  const skipBuild = args.includes('--skip-build') || args.includes('-s');
  
  try {
    log('🚀 Starting deployment process...', 'bright');
    
    // Bump version if requested
    if (bumpVersion) {
      log('📈 Bumping version...', 'yellow');
      try {
        execSync('npm version patch --no-git-tag-version', { stdio: 'inherit' });
      } catch (error) {
        log('⚠️ Could not bump version automatically, continuing with current version...', 'yellow');
      }
    }
    
    // Update environment file
    const versionInfo = updateEnvFile();
    
    // Build project
    if (!skipBuild) {
      buildProject();
      generateCacheBustingManifest();
    }
    
    log('🎉 Deployment process completed successfully!', 'green');
    log(`   Version: ${versionInfo.version}`, 'cyan');
    log(`   Build Hash: ${versionInfo.buildHash}`, 'cyan');
    
    // Instructions for users
    log('\n📋 Next steps:', 'bright');
    log('   1. Deploy the dist/ folder to your hosting service', 'cyan');
    log('   2. Users will automatically be notified of the update', 'cyan');
    log('   3. They can refresh to get the latest version', 'cyan');
    
  } catch (error) {
    log(`❌ Deployment failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run if called directly
const isMainModule = import.meta.url === `file://${process.argv[1]}` ||
                     import.meta.url.endsWith(process.argv[1]) ||
                     import.meta.url.includes('deploy.js');

if (isMainModule) {
  main();
}

export { updateEnvFile, buildProject, generateCacheBustingManifest };
