# PowerShell script to fix EMFILE errors on Windows by temporarily removing problematic MUI icons

Write-Host "🔧 Fixing EMFILE error on Windows..." -ForegroundColor Cyan

# Kill any existing Node.js processes
Write-Host "🛑 Stopping all Node.js processes..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 3

# Clear all caches aggressively
Write-Host "🧹 Clearing all caches..." -ForegroundColor Yellow
npm cache clean --force

# Remove Vite cache
if (Test-Path "node_modules/.vite") {
    Write-Host "🗑️ Removing Vite cache..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "node_modules/.vite"
}

# Remove dist folder
if (Test-Path "dist") {
    Write-Host "🗑️ Removing dist folder..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "dist"
}

# Temporarily rename MUI icons folder to prevent Vite from scanning it
$muiIconsPath = "node_modules/@mui/icons-material"
$muiIconsBackupPath = "node_modules/@mui/icons-material.backup"

if (Test-Path $muiIconsPath) {
    Write-Host "📦 Temporarily moving MUI icons to prevent file scanning..." -ForegroundColor Yellow
    if (Test-Path $muiIconsBackupPath) {
        Remove-Item -Recurse -Force $muiIconsBackupPath
    }
    Move-Item $muiIconsPath $muiIconsBackupPath
    
    # Create a minimal replacement with only the icons we actually use
    New-Item -ItemType Directory -Path $muiIconsPath -Force
    New-Item -ItemType Directory -Path "$muiIconsPath/esm" -Force
    
    # Create minimal exports for the icons we use
    $iconsWeUse = @(
        "ArrowBack", "ArrowBackIcon", "ContentCopy", "ContentCopyIcon", 
        "Download", "DownloadIcon", "CloudUpload", "CloudUploadIcon",
        "BarChart", "BarChartIcon", "Preview", "PreviewIcon",
        "Edit", "EditIcon", "Insights", "InsightsIcon",
        "Close", "CloseIcon", "ExpandMore", "ExpandMoreIcon",
        "Restore", "RestoreIcon", "Dataset", "DatasetIcon",
        "Launch", "LaunchIcon", "UploadFile", "UploadFileIcon"
    )
    
    foreach ($icon in $iconsWeUse) {
        $iconContent = @"
import React from 'react';
const $icon = (props) => React.createElement('div', { ...props, 'data-icon': '$icon' }, '$icon');
export default $icon;
"@
        Set-Content -Path "$muiIconsPath/esm/$icon.js" -Value $iconContent
    }
    
    # Create package.json for the minimal icons
    $packageJson = @"
{
  "name": "@mui/icons-material",
  "version": "5.0.0",
  "main": "index.js",
  "type": "module"
}
"@
    Set-Content -Path "$muiIconsPath/package.json" -Value $packageJson
}

Write-Host "✅ EMFILE fix applied!" -ForegroundColor Green

# Set environment variables for better performance
$env:NODE_OPTIONS = "--max-old-space-size=8192 --max-semi-space-size=512"
$env:VITE_FORCE_OPTIMIZE_DEPS = "true"

Write-Host "🚀 Starting development server with optimizations..." -ForegroundColor Green
npm run dev

Write-Host "🎉 Development server started!" -ForegroundColor Green
Write-Host "⚠️  Note: MUI icons are temporarily replaced with placeholders" -ForegroundColor Yellow
Write-Host "📝 To restore full icons after development, run: npm run restore:icons" -ForegroundColor Cyan
