import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  useTheme,
  useMediaQuery
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ProfessionalButton from './common/ProfessionalButton';
import ErrorDisplay from './common/ErrorDisplay';
import TimeoutErrorDisplay from './common/TimeoutErrorDisplay';
import { getChartInsights, ApiError, TimeoutError } from '../services/apiService';
import Chart from './Chart';
import config from '../config';

// Utility function for smooth scroll to top
const scrollToTop = (smooth: boolean = true) => {
  // First try to scroll the main content area
  const mainContentArea = document.querySelector('[data-main-content="true"]');
  if (mainContentArea) {
    mainContentArea.scrollTo({
      top: 0,
      left: 0,
      behavior: smooth ? 'smooth' : 'auto'
    });
  }
  // Also scroll window to top as fallback
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: smooth ? 'smooth' : 'auto'
  });
};

interface ChartInsightsPageProps {
  chartData: any;
  onBack: () => void;
}

interface InsightsResponse {
  success: boolean;
  chart_insights: string[];
  // Processed/categorized insights (derived from chart_insights)
  executive_summary: string[];
  data_insights: string[];
  hidden_patterns: string[];
  recommendations: string[];
  key_metrics: any;
  chart_metadata: any;
}

// Helper function to categorize insights based on their prefixes
const categorizeInsights = (chartInsights: string[]) => {
  const categorized = {
    executive_summary: [] as string[],
    data_insights: [] as string[],
    hidden_patterns: [] as string[],
    recommendations: [] as string[]
  };

  chartInsights.forEach(insight => {
    const lowerInsight = insight.toLowerCase();
    const cleanInsight = insight.replace(/^(data insight|hidden pattern|recommendation):\s*/i, '');

    if (lowerInsight.startsWith('data insight:')) {
      categorized.data_insights.push(cleanInsight);
    } else if (lowerInsight.startsWith('hidden pattern:')) {
      categorized.hidden_patterns.push(cleanInsight);
    } else if (lowerInsight.startsWith('recommendation:')) {
      categorized.recommendations.push(cleanInsight);
    } else {
      // If no specific prefix, treat as executive summary
      categorized.executive_summary.push(cleanInsight);
    }
  });

  return categorized;
};

// Helper function to parse and validate insights response
const parseInsightsResponse = (response: any): InsightsResponse => {
  console.log('Parsing insights response:', JSON.stringify(response, null, 2));
  const defaultInsights: InsightsResponse = {
    success: true,
    chart_insights: [],
    executive_summary: [],
    data_insights: [],
    hidden_patterns: [],
    recommendations: [],
    key_metrics: {},
    chart_metadata: {}
  };

  if (!response || typeof response !== 'object') {
    console.warn('Invalid insights response, using defaults');
    return defaultInsights;
  }

  try {
    // Get the raw chart_insights array from the new API format
    const chartInsights = Array.isArray(response.chart_insights) ? response.chart_insights : [];

    // Robust error detection for insights
    const isErrorInsight = (insight: string) => {
      const lowerInsight = insight.toLowerCase();
      return lowerInsight.includes('error') ||
             lowerInsight.includes('429') ||
             lowerInsight.includes('capacity exceeded') ||
             lowerInsight.includes('rate limit') ||
             lowerInsight.includes('failed');
    };

    // Check if any insights contain error messages
    if (chartInsights.some(isErrorInsight)) {
      console.warn('Chart insights contain error messages (e.g., 429), treating as invalid');
      return {
        ...defaultInsights,
        chart_insights: chartInsights,
        key_metrics: response.key_metrics || {},
        chart_metadata: response.chart_metadata || {},
        success: false // Mark as failed to trigger error UI
      };
    }

    // Categorize insights based on their prefixes
    const categorized = categorizeInsights(chartInsights);

    const parsed: InsightsResponse = {
      success: response.success ?? true,
      chart_insights: chartInsights,
      executive_summary: categorized.executive_summary,
      data_insights: categorized.data_insights,
      hidden_patterns: categorized.hidden_patterns,
      recommendations: categorized.recommendations,
      key_metrics: response.key_metrics || {},
      chart_metadata: response.chart_metadata || {}
    };

    console.log('Parsed and categorized insights:', JSON.stringify(parsed, null, 2));
    return parsed;
  } catch (err) {
    console.error('Error parsing insights response:', err);
    return defaultInsights;
  }
};

// Loading steps for progressive display
const LOADING_STEPS = [
  { id: 'data_insights', label: 'Data Insights', icon: '📈', color: '#3498db' },
  { id: 'hidden_patterns', label: 'Hidden Patterns', icon: '🔍', color: '#9b59b6' },
  { id: 'recommendations', label: 'Recommendations', icon: '💡', color: '#27ae60' }
];

const ChartInsightsPage: React.FC<ChartInsightsPageProps> = ({ chartData, onBack }) => {
  const [insights, setInsights] = useState<InsightsResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const hasFetchedRef = useRef(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  const currentChartRef = useRef<string>('');
  const stepTimeoutsRef = useRef<NodeJS.Timeout[]>([]);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const fetchInsights = async (chartId: string) => {
    console.log('Starting fetchInsights for chart:', chartData?.id, chartData?.chart_type);
    try {
      setError(null);
      setIsLoading(true);
      hasFetchedRef.current = true;

      // Fallback to prevent stuck loading
      loadingTimeoutRef.current = setTimeout(() => {
        console.warn('Loading timeout triggered');
        setIsLoading(false);
        setError('Request took too long. Please try again.');
        hasFetchedRef.current = false;
      }, 10000);

      const insightsKey = `chart_insights_${chartData.id}_${chartData.chart_type}`;
      const cachedInsights = sessionStorage.getItem(`${insightsKey}_data`);
      if (cachedInsights) {
        console.log('Using cached insights:', cachedInsights);
        try {
          const parsedCachedInsights = JSON.parse(cachedInsights);
          setInsights(() => parsedCachedInsights);
          setIsLoading(false);
          clearTimeout(loadingTimeoutRef.current!);
          setCompletedSteps(LOADING_STEPS.map(step => step.id));
          setCurrentStep(LOADING_STEPS.length);
          console.log('Cached insights applied');
          return;
        } catch (err) {
          console.error('Error parsing cached insights:', err);
          sessionStorage.removeItem(`${insightsKey}_data`);
        }
      }

      abortControllerRef.current = new AbortController();

      // Simulate loading steps with configurable timing
      stepTimeoutsRef.current = LOADING_STEPS.map((_, index) =>
        setTimeout(() => {
          setCurrentStep(index);
          if (index > 0) {
            setCompletedSteps(prev => [...prev, LOADING_STEPS[index - 1].id]);
          }
        }, config.insightsStepDuration * index)
      );

      const requestData = {
        chart_data: {
          type: chartData.chart_type,
          ...chartData.data
        },
        chart_type: chartData.chart_type,
        chart_title: chartData.layout?.title || 'Chart'
      };

      console.log('Fetching insights with request:', JSON.stringify(requestData, null, 2));
      const response = await getChartInsights(chartData.id, requestData);
      console.log('API response received:', JSON.stringify(response, null, 2));

      clearTimeout(loadingTimeoutRef.current!);
      stepTimeoutsRef.current.forEach(clearTimeout);
      stepTimeoutsRef.current = [];

      // Process response even if aborted, unless chart changed
      if (abortControllerRef.current.signal.aborted && currentChartRef.current !== chartId) {
        console.log('Request aborted due to chart change');
        hasFetchedRef.current = false;
        setIsLoading(false);
        return;
      }

      const parsedInsights = parseInsightsResponse(response);
      console.log('Setting insights state:', JSON.stringify(parsedInsights, null, 2));
      setInsights(() => parsedInsights);
      setIsLoading(() => false);
      setCompletedSteps(LOADING_STEPS.map(step => step.id));
      setCurrentStep(LOADING_STEPS.length);
      console.log('State updated: insights set, loading false');

      if (!parsedInsights.success) {
        setError('Failed to generate insights due to service capacity limits. Please try again later.');
      }

      sessionStorage.setItem(`${insightsKey}_data`, JSON.stringify(parsedInsights));
    } catch (err) {
      clearTimeout(loadingTimeoutRef.current!);
      stepTimeoutsRef.current.forEach(clearTimeout);
      stepTimeoutsRef.current = [];

      if (abortControllerRef.current?.signal.aborted && currentChartRef.current !== chartId) {
        console.log('Request aborted in catch block due to chart change');
        hasFetchedRef.current = false;
        setIsLoading(false);
        return;
      }

      hasFetchedRef.current = false;
      const insightsKey = `chart_insights_${chartData.id}_${chartData.chart_type}`;
      sessionStorage.removeItem(insightsKey);
      sessionStorage.removeItem(`${insightsKey}_data`);

      // Handle structured API errors
      let errorMessage = 'Failed to generate insights. Please try again.';

      if (err && typeof err === 'object' && 'message' in err && 'type' in err) {
        // This is an ApiError from the service
        const apiError = err as ApiError;
        errorMessage = typeof apiError.message === 'string' ? apiError.message : 'Failed to generate insights. Please try again.';
        console.error('API Error fetching insights:', {
          message: apiError.message,
          statusCode: apiError.statusCode,
          errorCode: apiError.errorCode,
          backendErrorType: apiError.backendErrorType,
          type: apiError.type
        });
      } else if (err instanceof TimeoutError) {
        // Handle timeout errors specifically
        errorMessage = err.message;
        console.error('Timeout error fetching insights:', err);
      } else if (err instanceof Error) {
        errorMessage = err.message;
        console.error('Error fetching insights:', err);
      } else {
        console.error('Unknown error fetching insights:', err);
      }

      // Legacy error handling for specific cases
      if (errorMessage.includes('429') || errorMessage.includes('capacity exceeded')) {
        setError('Service capacity exceeded. Please try again later or upgrade your plan.');
      } else {
        setError(errorMessage);
      }
      setIsLoading(false);
      console.log('Error state set:', errorMessage);
    }
  };

  useEffect(() => {
    if (!chartData?.id || !chartData?.chart_type) {
      console.warn('Invalid chartData:', chartData);
      setError('Invalid chart data provided');
      setIsLoading(false);
      return;
    }

    const currentChartId = `${chartData.id}_${chartData.chart_type}`;
    console.log('useEffect triggered, currentChartId:', currentChartId, 'chartData:', JSON.stringify(chartData, null, 2));

    if (currentChartRef.current !== currentChartId) {
      console.log('Chart changed, resetting state');
      currentChartRef.current = currentChartId;
      hasFetchedRef.current = false;
      setInsights(null);
      setError(null);
      setIsLoading(true);
      setCurrentStep(0);
      setCompletedSteps([]);
    }

    if (!hasFetchedRef.current) {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      debounceTimeoutRef.current = setTimeout(() => {
        console.log('Initiating fetchInsights after debounce');
        fetchInsights(currentChartId);
      }, 300);
    }

    return () => {
      console.log('Cleaning up useEffect');
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      stepTimeoutsRef.current.forEach(clearTimeout);
      stepTimeoutsRef.current = [];
    };
  }, [chartData?.id, chartData?.chart_type]);

  // Scroll to top when component mounts
  useEffect(() => {
    scrollToTop();
  }, []);

  console.log('Rendering ChartInsightsPage, state:', { isLoading, error, insights: !!insights });

  if (!chartData) {
    return (
      <ErrorDisplay
        error="Chart data not available"
        title="Chart Not Found"
        onGoBack={onBack}
        showActions={true}
        variant="page"
      />
    );
  }

  if (error) {
    // Check if it's a timeout error
    const isTimeoutError = error.toLowerCase().includes('timeout') ||
                          error.toLowerCase().includes('timed out') ||
                          error.toLowerCase().includes('took too long');

    if (isTimeoutError) {
      return (
        <TimeoutErrorDisplay
          title="Insights Generation Timeout"
          message={error}
          operationType="insights"
          onRetry={() => { hasFetchedRef.current = false; fetchInsights(currentChartRef.current); }}
          onGoBack={onBack}
          variant="page"
          showProgress={true}
        />
      );
    }

    return (
      <ErrorDisplay
        error={error}
        title="Unable to Generate Insights"
        onRetry={() => { hasFetchedRef.current = false; fetchInsights(currentChartRef.current); }}
        onGoBack={onBack}
        showActions={true}
        variant="page"
      />
    );
  }

  return (
    <Box sx={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      p: 3,
      backgroundColor: 'white',
      minHeight: '100vh',
    }}>
      {/* Page Header - Back button on left, title in center, other buttons on right */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 3,
      }}>
        {/* Left side - Back button */}
        <ProfessionalButton
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={onBack}
        >
          Back
        </ProfessionalButton>

        {/* Center - Page title */}
        <Typography
          variant="h4"
          sx={{
            fontWeight: 'bold',
            color: 'text.primary',
            textAlign: 'center',
            flex: 1,
            mx: 2,
          }}
        >
          Chart Insights
        </Typography>

        {/* Right side - Placeholder for future buttons */}
        <Box sx={{ width: '80px' }}></Box>
      </Box>

      {/* Charts Section - Same structure as Additional Charts page */}
      <Box sx={{
        width: '100%',
        backgroundColor: 'white',
        p: { xs: 2, sm: 2.5, md: 3 },
        flexGrow: 1,
        overflow: 'auto',
      }}>
        <EnhancedInsightsLayout
          chartData={chartData}
          insights={insights}
          isLoading={isLoading}
          isMobile={isMobile}
          currentStep={currentStep}
          completedSteps={completedSteps}
        />
      </Box>
    </Box>
  );
};

interface EnhancedInsightsLayoutProps {
  chartData: any;
  insights: InsightsResponse | null;
  isLoading: boolean;
  isMobile: boolean;
  currentStep: number;
  completedSteps: string[];
}

const StepLoading: React.FC<{ currentStep: number; completedSteps: string[] }> = ({ currentStep, completedSteps }) => (
  <Box
    sx={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'transparent',
      p: 4
    }}
  >
    <Typography variant="h6" sx={{ mb: 4, color: '#2c3e50', textAlign: 'center' }}>
      Generating Chart Insights
    </Typography>
    <Box sx={{ width: '100%', maxWidth: 400 }}>
      {LOADING_STEPS.map((step, index) => (
        <Box key={step.id} sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 3,
              backgroundColor: completedSteps.includes(step.id) ? step.color : index === currentStep ? `${step.color}20` : '#f5f5f5',
              border: index === currentStep ? `2px solid ${step.color}` : 'none',
              transition: 'all 0.3s ease'
            }}
          >
            {completedSteps.includes(step.id) ? (
              <Typography sx={{ color: 'white', fontSize: '1.2rem' }}>✓</Typography>
            ) : (
              <Typography sx={{ fontSize: '1.2rem' }}>{step.icon}</Typography>
            )}
          </Box>
          <Box sx={{ flexGrow: 1 }}>
            <Typography
              variant="body1"
              sx={{
                color: completedSteps.includes(step.id) || index === currentStep ? step.color : '#6c757d',
                fontWeight: completedSteps.includes(step.id) || index === currentStep ? 600 : 400,
                transition: 'all 0.3s ease'
              }}
            >
              {step.label}
            </Typography>
            {index === currentStep && (
              <Box sx={{ width: '100%', height: 3, backgroundColor: '#f0f0f0', borderRadius: 1.5, mt: 1, overflow: 'hidden' }}>
                <Box
                  sx={{
                    width: '100%',
                    height: '100%',
                    backgroundColor: step.color,
                    borderRadius: 1.5,
                    animation: 'progress 1.8s ease-in-out infinite'
                  }}
                />
              </Box>
            )}
          </Box>
        </Box>
      ))}
    </Box>
    <style>
      {`
        @keyframes progress {
          0% { transform: translateX(-100%); }
          50% { transform: translateX(0%); }
          100% { transform: translateX(100%); }
        }
      `}
    </style>
  </Box>
);

const ConsolidatedInsights: React.FC<{
  insights: InsightsResponse | null;
  isLoading: boolean;
  currentStep: number;
  completedSteps: string[];
  chartData: any;
}> = ({ insights, isLoading, currentStep, completedSteps, chartData }) => {
  console.log('Rendering ConsolidatedInsights, props:', { isLoading, insights, hasValidInsights: !!insights });

  const hasValidInsights = insights && insights.success && (
    insights.chart_insights.length > 0
  );

  const handleCopyInsights = React.useCallback(async (event: React.MouseEvent<HTMLElement>) => {
    if (!insights || !hasValidInsights) {
      console.warn('No insights available to copy');
      return;
    }

    // Get the specific button that was clicked
    const copyButton = event.currentTarget;
    let originalContent = '';
    let originalTitle = '';

    if (copyButton) {
      originalContent = copyButton.innerHTML;
      originalTitle = copyButton.getAttribute('title') || '';
      copyButton.innerHTML = '⏳';
      copyButton.setAttribute('title', 'Copying...');
    }

    // Copy all chart insights as received from backend
    const allInsights = insights.chart_insights && insights.chart_insights.length > 0
      ? [
          '📊 Chart Insights:',
          ...insights.chart_insights.map(item => `• ${item}`)
        ].join('\n')
      : 'No insights available';

    try {
      await navigator.clipboard.writeText(allInsights);

      // Show success message by temporarily updating the copy button
      if (copyButton) {
        copyButton.innerHTML = '✅';
        copyButton.setAttribute('title', 'Copied successfully!');

        setTimeout(() => {
          copyButton.innerHTML = originalContent;
          copyButton.setAttribute('title', originalTitle);
        }, 2000);
      }

      console.log('✅ Insights copied to clipboard successfully');
    } catch (err) {
      console.error('❌ Failed to copy insights:', err);

      // Show error message by temporarily updating the copy button
      if (copyButton) {
        copyButton.innerHTML = '❌';
        copyButton.setAttribute('title', 'Copy failed - try again');

        setTimeout(() => {
          copyButton.innerHTML = originalContent;
          copyButton.setAttribute('title', originalTitle);
        }, 2000);
      }
    }
  }, [insights, hasValidInsights]);

  if (isLoading) {
    return <StepLoading currentStep={currentStep} completedSteps={completedSteps} />;
  }

  if (!hasValidInsights) {
    return (
      <Paper
        elevation={8}
        sx={{
          width: '100%',
          aspectRatio: '1.5/1',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'white',
          borderRadius: 3,
          border: '1px solid rgba(0,0,0,0.05)',
          overflow: 'hidden',
          boxShadow: '0 8px 24px rgba(0,0,0,0.12)'
        }}
      >
        <Box sx={{ textAlign: 'center', p: 3 }}>
          <Typography variant="h6" sx={{ color: '#6c757d', mb: 2 }}>
            No Insights Available
          </Typography>
          <Typography variant="body2" sx={{ color: '#6c757d' }}>
            No insights could be generated for this chart. Try a different chart or wait a moment before retrying.
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: 'transparent',
        position: 'relative', // Add relative positioning for absolute child
      }}
    >
      {/* Copy Button - Fixed overlay at top right corner, independent of scroll */}
      {hasValidInsights && (
        <Box
          component="button"
          onClick={handleCopyInsights}
          data-copy-button={`insights-${chartData?.id || 'chart'}`}
          title="Copy All Insights"
          sx={{
            position: 'absolute',
            top: 10,
            right: 10,
            zIndex: 10,
            cursor: 'pointer',
            background: 'rgba(255, 255, 255, 0.9)',
            border: '1px solid rgba(0, 0, 0, 0.08)',
            padding: '4px',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
            }
          }}
        >
          <ContentCopyIcon sx={{ fontSize: '1.2rem', color: '#4caf50' }} />
        </Box>
      )}

      <Box sx={{
        p: 3,
        flexGrow: 1,
        overflowY: 'auto',
        '&::-webkit-scrollbar': { width: '8px' },
        '&::-webkit-scrollbar-track': { background: '#f1f1f1', borderRadius: '4px' },
        '&::-webkit-scrollbar-thumb': { background: '#c1c1c1', borderRadius: '4px', '&:hover': { background: '#a8a8a8' } }
      }}>
        {/* Display all chart insights as a single bullet list */}
        {insights?.chart_insights && insights.chart_insights.length > 0 ? (
          insights.chart_insights.map((insight, index) => (
            <Typography
              key={index}
              variant="body2"
              sx={{
                mb: 1.5,
                color: '#2c3e50',
                lineHeight: 1.6,
                fontSize: '0.875rem',
                '&:before': {
                  content: '"•"',
                  color: '#3498db',
                  fontWeight: 'bold',
                  display: 'inline-block',
                  width: '1em',
                  marginLeft: '0.5em'
                }
              }}
            >
              {insight}
            </Typography>
          ))
        ) : (
          <Typography variant="body2" sx={{ color: '#6c757d', fontStyle: 'italic', textAlign: 'center', mt: 4 }}>
            No insights available
          </Typography>
        )}
      </Box>
    </Box>
  );
};

const EnhancedInsightsLayout: React.FC<EnhancedInsightsLayoutProps> = ({ chartData, insights, isLoading, isMobile, currentStep, completedSteps }) => {
  console.log('Rendering EnhancedInsightsLayout, props:', { isLoading, hasInsights: !!insights, isMobile });

  if (isMobile) {
    return (
      <Box sx={{
        width: '100%',
        display: 'grid',
        gridTemplateColumns: '1fr',
        gap: 2,
      }}>
        <Paper
          elevation={0}
          sx={{
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: 2,
            overflow: 'hidden',
            width: '100%',
            aspectRatio: '1.4/1', // Increased aspect ratio for larger chart image
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
            boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
          }}
        >
          <Chart data={chartData} position="center" hideInsightsIcon={true} />
        </Paper>
        <Paper
          elevation={0}
          sx={{
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: 2,
            overflow: 'hidden',
            width: '100%',
            aspectRatio: { xs: '1.2/1', sm: '1.3/1', md: '1.4/1' },
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
            boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
            minHeight: { xs: '300px', sm: '350px', md: '400px' },
          }}
        >
          <ConsolidatedInsights insights={insights} isLoading={isLoading} currentStep={currentStep} completedSteps={completedSteps} chartData={chartData} />
        </Paper>
      </Box>
    );
  }

  return (
    <Box sx={{
      width: '100%',
      display: 'grid',
      gridTemplateColumns: {
        xs: '1fr',
        sm: '1fr',
        md: 'repeat(2, 1fr)',
      },
      gap: { xs: 1.5, sm: 2 },
      '@media (max-width: 900px)': {
        gridTemplateColumns: '1fr',
      }
    }}>
      <Paper
        elevation={0}
        sx={{
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderRadius: 2,
          overflow: 'hidden',
          width: '100%',
          aspectRatio: { xs: '1.2/1', sm: '1.3/1', md: '1.4/1' },
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
          minHeight: { xs: '300px', sm: '350px', md: '400px' },
        }}
      >
        <Chart data={chartData} position="center" hideInsightsIcon={true} />
      </Paper>
      <Paper
        elevation={0}
        sx={{
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderRadius: 2,
          overflow: 'hidden',
          width: '100%',
          aspectRatio: { xs: '1.2/1', sm: '1.3/1', md: '1.4/1' },
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
          minHeight: { xs: '300px', sm: '350px', md: '400px' },
        }}
      >
        <ConsolidatedInsights insights={insights} isLoading={isLoading} currentStep={currentStep} completedSteps={completedSteps} chartData={chartData} />
      </Paper>
    </Box>
  );
};

export default ChartInsightsPage;