import React from 'react';
import { <PERSON>, Typo<PERSON>, Grid, Card, CardContent, Button, Stack } from '@mui/material';
import { useApiErrorModal } from '../contexts/ErrorModalContext';

/**
 * Demo component showing different error modal scenarios
 * This component demonstrates how to use the ErrorModal in various situations
 */
const ErrorModalDemo: React.FC = () => {
  const { modalProps, showError, showCustomError, handleApiError, handleUploadError, handleChartError, handleAuthError } = useApiErrorModal();

  // Example error scenarios
  const showNetworkError = () => {
    const error = new Error('Failed to fetch data from server');
    showError(error, {
      context: 'Network Request',
      onRetry: () => {
        console.log('Retrying network request...');
        // Simulate retry logic
      }
    });
  };

  const showTimeoutError = () => {
    const error = new Error('Request timeout after 30 seconds');
    showError(error, {
      context: 'API Timeout',
      customTitle: 'Request Timeout',
      customDescription: 'The request is taking longer than expected. This might be due to a slow internet connection or server processing time.',
      onRetry: () => {
        console.log('Retrying timeout request...');
      }
    });
  };

  const showAuthenticationError = () => {
    const error = { status: 401, message: 'Token expired' };
    handleAuthError(error, () => {
      console.log('Redirecting to sign-in...');
      // Redirect to sign-in page
    });
  };

  const showValidationError = () => {
    const error = { status: 400, message: 'Invalid file format. Please upload a CSV file.' };
    showError(error, {
      context: 'File Validation',
      customTitle: 'Invalid File Format',
      customDescription: 'The uploaded file format is not supported. Please upload a valid CSV file and try again.',
      backLabel: 'Choose Different File'
    });
  };

  const showServerError = () => {
    const error = { status: 500, message: 'Internal server error' };
    handleApiError(error, () => {
      console.log('Retrying server request...');
    }, 'Server Communication');
  };

  const showUploadError = () => {
    const error = new Error('File upload failed: File size too large');
    handleUploadError(error, () => {
      console.log('Retrying file upload...');
    });
  };

  const showChartError = () => {
    const error = new Error('Chart generation failed: Invalid data format');
    handleChartError(error, () => {
      console.log('Retrying chart generation...');
    });
  };

  const showCustomErrorExample = () => {
    showCustomError({
      title: 'Custom Error Example',
      description: 'This is a custom error message with specific styling and behavior.',
      errorId: 'custom_error_12345',
      technicalDetails: {
        error: 'Custom error for demonstration',
        stack: 'CustomError: This is a demo error\n    at showCustomErrorExample (ErrorModalDemo.tsx:67:5)',
        context: 'Custom Error Demo',
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      },
      onRetry: () => {
        console.log('Custom retry action...');
      },
      onBack: () => {
        console.log('Custom back action...');
      },
      retryLabel: 'Try Custom Action',
      backLabel: 'Go to Dashboard'
    });
  };

  const showJavaScriptError = () => {
    // Simulate a JavaScript error like the one that was fixed
    const error = new ReferenceError('Cannot access \'isStackedBar\' before initialization');
    error.stack = `ReferenceError: Cannot access 'isStackedBar' before initialization
    at http://localhost:5173/src/components/Chart.tsx?t=1750662872099:995:5
    at Chart (http://localhost:5173/src/components/Chart.tsx?t=1750662872099:1302:23)
    at react-stack-bottom-frame (http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=ee35d623:17424:20)
    at renderWithHooks (http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=ee35d623:4206:24)`;
    
    showError(error, {
      context: 'Chart Rendering',
      customTitle: 'Chart Rendering Error',
      customDescription: 'There was an issue rendering the chart. This appears to be a technical problem that our development team needs to address.',
      onRetry: () => {
        console.log('Retrying chart render...');
      }
    });
  };

  return (
    <Box sx={{ p: 4, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" sx={{ mb: 4, textAlign: 'center', fontWeight: 600 }}>
        Error Modal Demo
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 4, textAlign: 'center', color: 'text.secondary' }}>
        This page demonstrates different error scenarios and how they are handled by the compact ErrorModal component.
        Click any button below to see the corresponding error modal. The modal is now 50% smaller with no vertical scrollbars.
      </Typography>

      <Grid container spacing={3}>
        {/* Network Errors */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, color: 'primary.main' }}>
                Network Errors
              </Typography>
              <Stack spacing={2}>
                <Button variant="outlined" onClick={showNetworkError} fullWidth>
                  Network Connection Error
                </Button>
                <Button variant="outlined" onClick={showTimeoutError} fullWidth>
                  Request Timeout Error
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Authentication & Authorization */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, color: 'warning.main' }}>
                Auth Errors
              </Typography>
              <Stack spacing={2}>
                <Button variant="outlined" onClick={showAuthenticationError} fullWidth>
                  Authentication Required
                </Button>
                <Button variant="outlined" onClick={showValidationError} fullWidth>
                  Validation Error
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Server Errors */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, color: 'error.main' }}>
                Server Errors
              </Typography>
              <Stack spacing={2}>
                <Button variant="outlined" onClick={showServerError} fullWidth>
                  Internal Server Error
                </Button>
                <Button variant="outlined" onClick={showJavaScriptError} fullWidth>
                  JavaScript Error
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Application-Specific Errors */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, color: 'info.main' }}>
                App-Specific Errors
              </Typography>
              <Stack spacing={2}>
                <Button variant="outlined" onClick={showUploadError} fullWidth>
                  File Upload Error
                </Button>
                <Button variant="outlined" onClick={showChartError} fullWidth>
                  Chart Processing Error
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Custom Errors */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, color: 'secondary.main' }}>
                Custom Errors
              </Typography>
              <Stack spacing={2}>
                <Button variant="outlined" onClick={showCustomErrorExample} fullWidth>
                  Custom Error Modal
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Technical Details Info */}
      <Box sx={{ mt: 4, p: 3, backgroundColor: 'grey.50', borderRadius: 2 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Technical Details Configuration
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          <strong>Current Environment:</strong> {import.meta.env.MODE}
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          <strong>Show Technical Details:</strong> {import.meta.env.MODE === 'development' ? 'Yes (Development Mode)' : 'No (Production Mode)'}
        </Typography>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          Technical details are automatically shown in development mode and hidden in production.
          You can override this by setting VITE_SHOW_TECHNICAL_DETAILS=true in your .env file.
          Scrollbars only appear when technical details are expanded.
        </Typography>
      </Box>

      {/* Error Modal is now handled globally by ErrorModalProvider */}
    </Box>
  );
};

export default ErrorModalDemo;
