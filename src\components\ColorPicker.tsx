import React from 'react';
import { Box, Typography, Paper } from '@mui/material';

interface ColorPickerProps {
  value: string;
  onChange: (color: string) => void;
}

const ColorPicker: React.FC<ColorPickerProps> = ({ value, onChange }) => {
  const colors = [
    '#f5f5f5', // Default light gray
    '#ffffff', // White
    '#fafafa', // Slightly off-white
    '#eeeeee', // Light gray
    '#e0e0e0', // Medium light gray
    '#f0f4f8', // Cool light gray
    '#f5f7fa', // Blue-tinted light gray
    '#f8f9fa', // Warm light gray
    '#f0f2f5', // Facebook-like gray
    '#f7f7f7', // Twitter-like gray
  ];

  return (
    <Paper sx={{ p: 2 }}>
      <Typography variant="subtitle2" sx={{ mb: 2, color: 'text.primary' }}>
        Background Color
      </Typography>
      
      <Box sx={{ 
        display: 'grid',
        gridTemplateColumns: 'repeat(5, 1fr)',
        gap: 1
      }}>
        {colors.map((color) => (
          <Box
            key={color}
            onClick={() => onChange(color)}
            sx={{
              width: '100%',
              paddingTop: '100%',
              backgroundColor: color,
              border: value === color ? '2px solid #2196f3' : '1px solid #e0e0e0',
              borderRadius: 1,
              cursor: 'pointer',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'scale(1.1)',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              },
            }}
          />
        ))}
      </Box>
    </Paper>
  );
};

export default ColorPicker;
