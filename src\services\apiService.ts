// src/services/apiService.ts

import config from '../config';

const API_BASE_URL = config.apiBaseUrl;

// Timeout error class for better error handling
export class TimeoutError extends Error {
  constructor(message: string, public timeoutMs: number) {
    super(message);
    this.name = 'TimeoutError';
  }
}

// Fetch with timeout wrapper
async function fetchWithTimeout(
  url: string,
  options: RequestInit = {},
  timeoutMs: number = config.apiTimeout
): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);

    if (error instanceof Error && error.name === 'AbortError') {
      throw new TimeoutError(
        `Request timed out after ${timeoutMs / 1000} seconds. Please check your internet connection and try again.`,
        timeoutMs
      );
    }
    throw error;
  }
}

// UUID generator - use crypto.randomUUID() if available, fallback to custom implementation
function generateUUID() {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  // Fallback for older browsers
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

// Device ID fetcher
export function getDeviceId(): string {
  let deviceId = localStorage.getItem('device_id');
  if (!deviceId) {
    deviceId = generateUUID();
    localStorage.setItem('device_id', deviceId);
  }
  return deviceId;
}

// Session ID fetcher (unique per login session)
export function getSessionId(): string {
  let sessionId = sessionStorage.getItem('session_id');
  if (!sessionId) {
    sessionId = generateUUID();
    sessionStorage.setItem('session_id', sessionId);
  }
  return sessionId;
}

// Call this on successful login to generate new session id
export function initializeSession() {
  const newSessionId = generateUUID();
  sessionStorage.setItem('session_id', newSessionId);
  return newSessionId;
}

// Always get access_token from localStorage for API calls
export function getAuthToken(): string | null {
  return localStorage.getItem('access_token');
}

export function getRefreshToken(): string | null {
  return localStorage.getItem('refresh_token');
}

export function setAuthToken(token: string) {
  localStorage.setItem('access_token', token);
}

export function setRefreshToken(token: string) {
  localStorage.setItem('refresh_token', token);
}

export function clearAuthToken() {
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
}



// Centralized header builder for JSON APIs with new common headers
export function getHeaders(
  contentType: string | null = 'application/json'
): Headers {
  const headers = new Headers();

  // New common headers as per backend requirements
  headers.append('x-request-id', generateUUID());
  headers.append('x-session-id', getSessionId());
  headers.append('x-device-id', getDeviceId());
  headers.append('x-client-version', '1.0.0');
  headers.append('x-platform', 'web');
  headers.append('x-timezone', Intl.DateTimeFormat().resolvedOptions().timeZone);

  // Content type header
  if (contentType) headers.append('Content-Type', contentType);

  // Authorization header
  const token = getAuthToken();
  if (token) headers.append('Authorization', `Bearer ${token}`);

  return headers;
}

// Callback for when authentication fails completely
let onAuthFailureCallback: (() => void) | null = null;

export function setAuthFailureCallback(callback: () => void) {
  onAuthFailureCallback = callback;
}

// Export tryRefreshToken for use in App.tsx
export async function tryRefreshToken(): Promise<{ success: boolean; newTokens?: any }> {
  const refreshToken = getRefreshToken();
  if (!refreshToken) {
    return { success: false };
  }

  try {
    const response = await fetchWithTimeout(`${API_BASE_URL}/auth/refresh-token`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    if (!response.ok) {
      // Refresh token is invalid or expired
      console.warn('Refresh token failed:', response.status);
      clearAuthToken(); // Clear invalid tokens
      return { success: false };
    }

    const data = await response.json();
    if (data.access_token) {
      setAuthToken(data.access_token);

      // Update refresh token if provided
      if (data.refresh_token) {
        setRefreshToken(data.refresh_token);
      }

      console.log('Token refreshed successfully');
      return { success: true, newTokens: data };
    }

    return { success: false };
  } catch (error) {
    console.error('Error refreshing token:', error);

    // Log timeout errors specifically
    if (error instanceof TimeoutError) {
      console.warn(`Token refresh timed out after ${error.timeoutMs / 1000} seconds`);
    }

    clearAuthToken(); // Clear tokens on error
    return { success: false };
  }
}

// Enhanced wrapper for API calls with token refresh logic, timeout, and better error handling
async function fetchWithAuthRetry(
  url: string,
  options: RequestInit,
  retry = true,
  timeoutMs: number = config.apiTimeout
): Promise<Response> {
  let token = getAuthToken();
  if (token && options.headers instanceof Headers) {
    options.headers.set('Authorization', `Bearer ${token}`);
  }

  let response = await fetchWithTimeout(url, options, timeoutMs);

  // Handle 401 Unauthorized responses with token refresh
  if (response.status === 401 && retry) {
    console.log('Received 401 Unauthorized, attempting token refresh...');

    try {
      const refreshResult = await tryRefreshToken();

      if (refreshResult.success) {
        // Token refresh successful, retry the original request
        token = getAuthToken();
        if (token && options.headers instanceof Headers) {
          options.headers.set('Authorization', `Bearer ${token}`);
        }
        console.log('Token refreshed successfully, retrying original request...');
        response = await fetchWithTimeout(url, options, timeoutMs);

        // If still 401 after refresh, the new token is also invalid
        if (response.status === 401) {
          console.error('Still receiving 401 after token refresh, forcing sign-in...');
          clearAuthToken();
          if (onAuthFailureCallback) {
            onAuthFailureCallback();
          }
        }
      } else {
        // Token refresh failed, trigger sign-in redirect
        console.warn('Token refresh failed, clearing tokens and redirecting to sign-in...');
        clearAuthToken();
        if (onAuthFailureCallback) {
          onAuthFailureCallback();
        }
      }
    } catch (refreshError) {
      console.error('Error during token refresh:', refreshError);
      clearAuthToken();
      if (onAuthFailureCallback) {
        onAuthFailureCallback();
      }
    }
  }

  return response;
}

/**
 * Get insights for a chart with Analytics Tracking
 */
export const getChartInsights = async (
  chartId: string | number,
  requestData: any
): Promise<any> => {
  const analyticsInstance = AnalyticsService.getInstance();

  try {
    // Track insights request start
    await analyticsInstance.trackChartEvent(
      'CHART_INSIGHTS',
      requestData?.chart_type,
      chartId,
      {
        request_start: new Date().toISOString()
      }
    );

    const headers = getHeaders();
    const response = await fetchWithAuthRetry(
      `${API_BASE_URL}/chart/insights`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify(requestData),
      }
    );

    if (!response.ok) {
      // Try to parse error response for structured error information
      let errorResponse = null;
      try {
        errorResponse = await response.json();
      } catch (parseError) {
        console.warn('Failed to parse error response:', parseError);
      }

      // Track insights error with structured error details
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'chart_insights_failed',
        status_code: response.status,
        chart_id: chartId,
        chart_type: requestData?.chart_type,
        error_code: errorResponse?.error_code,
        backend_error_type: errorResponse?.error_type,
        backend_error_message: errorResponse?.error_message
      });

      // Create user-friendly error message based on structured response
      let userMessage = "Unable to generate insights for this chart. Please try again.";
      let errorType: 'warning' | 'error' = 'warning';

      if (errorResponse?.error_message) {
        // Use backend error message as base
        const backendMessage = errorResponse.error_message;

        // Map specific error codes/types to user-friendly messages
        if (errorResponse.error_code === 4001 || backendMessage.toLowerCase().includes('no valid data points')) {
          userMessage = "No valid data points found for analysis. Please ensure your chart contains meaningful data and try again.";
          errorType = 'warning';
        } else if (errorResponse.error_type === 'BUSINESS') {
          userMessage = backendMessage; // Use backend message for business errors
          errorType = 'warning';
        } else if (response.status >= 500) {
          userMessage = "Unable to generate insights at the moment. Please try again later.";
          errorType = 'error';
        } else {
          userMessage = backendMessage; // Use backend message for other client errors
          errorType = 'warning';
        }
      } else {
        // Fallback to generic messages based on status code
        if (response.status >= 500) {
          userMessage = "Unable to generate insights at the moment. Please try again later.";
          errorType = 'error';
        } else if (response.status === 422) {
          userMessage = "The chart data is not suitable for insights generation. Please try a different chart.";
          errorType = 'warning';
        }
      }

      const apiError: ApiError = {
        message: userMessage,
        type: errorType,
        statusCode: response.status,
        errorCode: errorResponse?.error_code,
        backendErrorType: errorResponse?.error_type
      };
      throw apiError;
    }

    const result = await response.json();

    return result;
  } catch (error) {
    console.error('Error getting chart insights:', error);

    // Track unexpected errors
    try {
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'chart_insights_exception',
        error_message: error instanceof Error ? error.message : 'Unknown error',
        chart_id: chartId,
        chart_type: requestData?.chart_type,
        is_timeout: error instanceof TimeoutError
      });
    } catch (analyticsError) {
      console.warn('Failed to track insights error:', analyticsError);
    }

    // If it's already an ApiError, re-throw it
    if (error && typeof error === 'object' && 'type' in error) {
      throw error;
    }

    // Handle timeout errors specifically
    if (error instanceof TimeoutError) {
      const apiError: ApiError = {
        message: `Request timed out after ${error.timeoutMs / 1000} seconds. The server is taking longer than expected to generate insights. Please try again.`,
        type: 'error',
        statusCode: 0
      };
      throw apiError;
    }

    // For other errors, create a technical error
    const apiError: ApiError = {
      message: "Unable to connect to our servers. Please check your internet connection and try again.",
      type: 'error',
      statusCode: 0
    };
    throw apiError;
  }
};

/**
 * Update chart data with Analytics Tracking
 */
export const updateChart = async (
  chartId: string | number,
  updatedData: any
): Promise<any> => {
  const analyticsInstance = AnalyticsService.getInstance();

  try {
    // Track chart update start
    await analyticsInstance.trackChartEvent(
      'CHART_UPDATE',
      updatedData?.chart_type,
      chartId,
      {
        update_start: new Date().toISOString(),
        fields_updated: Object.keys(updatedData || {})
      }
    );

    const headers = getHeaders();
    const response = await fetchWithAuthRetry(
      `${API_BASE_URL}/chart/data`,
      {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          chart_id: chartId,
          updated_data: updatedData,
        }),
      }
    );

    if (!response.ok) {
      // Track update error
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'chart_update_failed',
        status_code: response.status,
        chart_id: chartId,
        chart_type: updatedData?.chart_type
      });

      const apiError: ApiError = {
        message: response.status >= 500
          ? "Unable to update chart at the moment. Please try again later."
          : "Unable to update chart with the provided data. Please check your changes and try again.",
        type: response.status >= 500 ? 'error' : 'warning',
        statusCode: response.status
      };
      throw apiError;
    }

    const result = await response.json();

    // Track successful chart update
    await analyticsInstance.trackChartEvent(
      'CHART_UPDATE',
      updatedData?.chart_type,
      chartId,
      {
        success: true,
        update_completed: new Date().toISOString(),
        fields_updated: Object.keys(updatedData || {})
      }
    );

    return result;
  } catch (error) {
    console.error('Error updating chart:', error);

    // Track unexpected errors
    try {
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'chart_update_exception',
        error_message: error instanceof Error ? error.message : 'Unknown error',
        chart_id: chartId,
        chart_type: updatedData?.chart_type,
        is_timeout: error instanceof TimeoutError
      });
    } catch (analyticsError) {
      console.warn('Failed to track update error:', analyticsError);
    }

    // If it's already an ApiError, re-throw it
    if (error && typeof error === 'object' && 'type' in error) {
      throw error;
    }

    // Handle timeout errors specifically
    if (error instanceof TimeoutError) {
      const apiError: ApiError = {
        message: `Request timed out after ${error.timeoutMs / 1000} seconds. The server is taking longer than expected to update the chart. Please try again.`,
        type: 'error',
        statusCode: 0
      };
      throw apiError;
    }

    // For other errors, create a technical error
    const apiError: ApiError = {
      message: "Unable to connect to our servers. Please check your internet connection and try again.",
      type: 'error',
      statusCode: 0
    };
    throw apiError;
  }
};

// Error types for different severity levels
export interface ApiError {
  message: string;
  type: 'warning' | 'error';
  statusCode?: number;
  errorCode?: number;
  backendErrorType?: string;
}

/**
 * Upload and process CSV data with Analytics Tracking
 */
export const uploadCSV = async (
  formData: FormData
): Promise<any> => {
  const file = formData.get('file') as File;
  const analyticsInstance = AnalyticsService.getInstance();

  // Start analytics tracking in parallel (non-blocking)
  if (file) {
    // Don't await - let analytics run in background
    analyticsInstance.trackFileUpload(
      file.name,
      file.size,
      file.type
    ).catch(error => {
      console.warn('Analytics tracking failed during file upload:', error);
    });
  }

  try {
    // Use getHeaders but without Content-Type for FormData
    const headers = getHeaders(null);

    const response = await fetchWithAuthRetry(
      `${API_BASE_URL}/data/file-upload`,
      {
        method: 'POST',
        headers,
        body: formData,
      }
    );

    // Always try to parse the response as JSON
    const responseData = await response.json();

    if (!response.ok) {
      // Track upload error (non-blocking)
      analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'file_upload_failed',
        status_code: response.status,
        file_name: file?.name,
        file_size: file?.size,
        file_type: file?.type
      }).catch(error => {
        console.warn('Analytics error tracking failed:', error);
      });

      // Create structured error based on status code
      const apiError: ApiError = {
        message: '',
        type: response.status >= 500 ? 'error' : 'warning',
        statusCode: response.status
      };

      // Set appropriate message based on status code
      if (response.status >= 400 && response.status < 500) {
        // Business/client errors - user can retry
        apiError.message = responseData.message ||
          "No charts could be generated from your data. Please check your file format and try again.";
      } else if (response.status >= 500) {
        // Server errors - technical issues
        apiError.message = "Our server is unable to process your request at the moment. Please try again later!";
      } else {
        apiError.message = "An unexpected error occurred. Please try again.";
      }

      throw apiError;
    }

    // Check if the response contains an error field (even with 200 status)
    if (responseData.error) {
      // Track business logic error (non-blocking)
      analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'file_processing_failed',
        error_message: responseData.error,
        file_name: file?.name,
        file_size: file?.size,
        file_type: file?.type
      }).catch(error => {
        console.warn('Analytics error tracking failed:', error);
      });

      const apiError: ApiError = {
        message: responseData.error,
        type: 'warning', // Treat response errors as business logic issues
        statusCode: 200
      };
      throw apiError;
    }

    // Track successful upload and chart generation (non-blocking)
    if (responseData.charts && Array.isArray(responseData.charts)) {
      analyticsInstance.trackEvent('CHART_GENERATION', {
        success: true,
        charts_generated: responseData.charts.length,
        file_name: file?.name,
        file_size: file?.size,
        file_type: file?.type,
        upload_duration: new Date().toISOString(),
        chart_types: responseData.charts.map((chart: any) => chart.chart_type)
      }).catch(error => {
        console.warn('Analytics success tracking failed:', error);
      });
    }

    return responseData;
  } catch (error) {
    console.error('Error uploading CSV:', error);

    // Track unexpected errors (non-blocking)
    analyticsInstance.trackEvent('ERROR_OCCURRED', {
      error_type: 'file_upload_exception',
      error_message: error instanceof Error ? error.message : 'Unknown error',
      file_name: file?.name,
      file_size: file?.size,
      file_type: file?.type,
      is_timeout: error instanceof TimeoutError
    }).catch(analyticsError => {
      console.warn('Failed to track upload error:', analyticsError);
    });

    // If it's already an ApiError, re-throw it
    if (error && typeof error === 'object' && 'type' in error) {
      throw error;
    }

    // Handle timeout errors specifically
    if (error instanceof TimeoutError) {
      const apiError: ApiError = {
        message: `Upload timed out after ${error.timeoutMs / 1000} seconds. Large files may take longer to process. Please try again or use a smaller file.`,
        type: 'error',
        statusCode: 0
      };
      throw apiError;
    }

    // For network errors or other unexpected errors
    const apiError: ApiError = {
      message: "Unable to connect to our servers. Please check your internet connection and try again.",
      type: 'error',
      statusCode: 0
    };
    throw apiError;
  }
};

/**
 * Google OAuth Login with Analytics Tracking
 */
export const googleLogin = async (googleToken: string): Promise<any> => {
  try {
    // Enhanced login data with analytics information
    const loginData = {
      token: googleToken,
      user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
      session_id: getSessionId(),
      device_id: getDeviceId(),
      // Add location if available from geolocation API
      timestamp: new Date().toISOString()
    };

    const response = await fetchWithTimeout(`${API_BASE_URL}/auth/google`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(loginData),
    });

    if (!response.ok) {
      throw new Error(`Google login failed: ${response.status}`);
    }
    const data = await response.json();

    // Save tokens after successful login
    if (data.access_token) {
      setAuthToken(data.access_token);

      // Save refresh token if provided
      if (data.refresh_token) {
        setRefreshToken(data.refresh_token);
      }

      initializeSession();

      // Update analytics session and track login event
      const analyticsInstance = AnalyticsService.getInstance();
      analyticsInstance.updateSessionId();

      // Track login event with user metadata
      await analyticsInstance.trackEvent('USER_LOGIN', {
        login_method: 'google',
        user_agent: loginData.user_agent,
        device_id: loginData.device_id,
        timestamp: loginData.timestamp
      });
    }

    return data;
  } catch (error) {
    console.error('Error during Google login:', error);

    // Track login error
    try {
      const analyticsInstance = AnalyticsService.getInstance();
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'login_failed',
        error_message: error instanceof Error ? error.message : 'Unknown login error',
        login_method: 'google',
        is_timeout: error instanceof TimeoutError
      });
    } catch (analyticsError) {
      console.warn('Failed to track login error:', analyticsError);
    }

    // Handle timeout errors specifically for login
    if (error instanceof TimeoutError) {
      throw new Error(`Login timed out after ${error.timeoutMs / 1000} seconds. Please check your internet connection and try again.`);
    }

    throw error;
  }
};

/**
 * Refresh access token
 */
export const refreshToken = async (refreshToken: string): Promise<any> => {
  try {
    const response = await fetchWithTimeout(`${API_BASE_URL}/auth/refresh-token`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    if (!response.ok) {
      throw new Error(`Token refresh failed: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error refreshing token:', error);

    // Handle timeout errors specifically for token refresh
    if (error instanceof TimeoutError) {
      throw new Error(`Token refresh timed out after ${error.timeoutMs / 1000} seconds. Please try signing in again.`);
    }

    throw error;
  }
};

// ============================================================================
// ANALYTICS SERVICE
// ============================================================================

// Event types for analytics tracking
export type AnalyticsEventType =
  | 'USER_LOGIN'
  | 'FILE_UPLOAD'
  | 'CHART_GENERATION'
  | 'CHART_RENDER'
  | 'CHART_DOWNLOAD'
  | 'ADDITIONAL_CHARTS_VIEW'
  | 'CHART_UPDATE'
  | 'CHART_COPY'
  | 'CHART_INSIGHTS'
  | 'NAVIGATION'
  | 'ERROR_OCCURRED';

// Analytics interfaces - Updated to match backend API contract
export interface AnalyticsEvent {
  event_type: AnalyticsEventType;
  session_id?: string;
  user_agent?: string;
  ip_address?: string;
  metadata?: Record<string, any>;
  timestamp?: string;
}

export interface FileUploadEvent {
  file_name: string;
  file_size: number;
  file_type: string;
  session_id?: string;
  user_agent?: string;
  ip_address?: string;
}

export interface ChartEvent {
  event_type: AnalyticsEventType;
  chart_type?: string;
  chart_id?: string | number;
  session_id?: string;
  user_agent?: string;
  ip_address?: string;
}

export interface PageViewEvent {
  page_url: string;
  page_title: string;
  session_id?: string;
  user_agent?: string;
  ip_address?: string;
  referrer?: string;
}

/**
 * Get client IP address (placeholder - in production this would be handled by backend)
 */
const getClientIPAddress = (): string => {
  // In a real application, the IP address would be determined by the backend
  // For now, we'll use a placeholder that the backend can override
  return 'client_ip';
};

/**
 * Analytics Service Class
 * Handles all analytics tracking with automatic retry and error handling
 */
export class AnalyticsService {
  private static instance: AnalyticsService | null = null;
  private sessionId: string;
  private isEnabled: boolean = true;
  private retryAttempts: number = 3;
  private retryDelay: number = 1000; // 1 second

  constructor() {
    this.sessionId = getSessionId();
  }

  // Singleton pattern to ensure one instance across the app
  public static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  // Enable/disable analytics tracking
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  // Update session ID (call this after login)
  public updateSessionId(): void {
    this.sessionId = getSessionId();
  }

  // Configure retry settings
  public setRetryConfig(attempts: number, delay: number): void {
    this.retryAttempts = Math.max(1, attempts);
    this.retryDelay = Math.max(100, delay);
  }

  // Validate event data before sending
  private validateEventData(eventType: AnalyticsEventType, metadata?: Record<string, any>): boolean {
    if (!eventType || typeof eventType !== 'string') {
      console.warn('Analytics: Invalid event type provided');
      return false;
    }

    if (metadata && typeof metadata !== 'object') {
      console.warn('Analytics: Invalid metadata provided');
      return false;
    }

    return true;
  }

  // Retry wrapper for analytics requests
  private async retryRequest(requestFn: () => Promise<Response>, context: string): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const response = await requestFn();

        if (response.ok) {
          return; // Success
        }

        // Don't retry client errors (4xx), only server errors (5xx) and network issues
        if (response.status >= 400 && response.status < 500) {
          console.warn(`Analytics ${context}: Client error ${response.status}, not retrying`);
          return;
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        if (attempt < this.retryAttempts) {
          console.warn(`Analytics ${context}: Attempt ${attempt} failed, retrying in ${this.retryDelay}ms...`, lastError.message);
          await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
        }
      }
    }

    console.warn(`Analytics ${context}: All ${this.retryAttempts} attempts failed`, lastError?.message);
  }

  /**
   * Track general events
   */
  public async trackEvent(
    eventType: AnalyticsEventType,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    if (!this.isEnabled) return;

    if (!this.validateEventData(eventType, metadata)) {
      return;
    }

    try {
      const headers = getHeaders();
      const eventData: AnalyticsEvent = {
        event_type: eventType,
        session_id: this.sessionId,
        user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
        ip_address: getClientIPAddress(),
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
          page_url: typeof window !== 'undefined' ? window.location.href : ''
        },
        timestamp: new Date().toISOString()
      };

      await this.retryRequest(
        () => fetchWithAuthRetry(
          `${API_BASE_URL}/analytics/events`,
          {
            method: 'POST',
            headers,
            body: JSON.stringify(eventData),
          }
        ),
        `general event: ${eventType}`
      );
    } catch (error) {
      console.warn('Analytics tracking failed for event:', eventType, error);
      // Don't throw error to avoid disrupting user experience
    }
  }

  /**
   * Track file upload events
   */
  public async trackFileUpload(
    fileName: string,
    fileSize: number,
    fileType: string
  ): Promise<void> {
    if (!this.isEnabled) return;

    // Validate required parameters
    if (!fileName || typeof fileName !== 'string') {
      console.warn('Analytics: Invalid file name provided for upload tracking');
      return;
    }

    if (typeof fileSize !== 'number' || fileSize < 0) {
      console.warn('Analytics: Invalid file size provided for upload tracking');
      return;
    }

    try {
      // Use the general trackEvent method instead of making duplicate calls
      // This is more efficient and avoids double API calls
      await this.trackEvent('FILE_UPLOAD', {
        file_name: fileName,
        file_size: fileSize,
        file_type: fileType || 'unknown',
        upload_start: new Date().toISOString()
      });
    } catch (error) {
      console.warn('Analytics tracking failed for file upload:', error);
    }
  }

  /**
   * Track chart-related events with duplicate prevention
   */
  public async trackChartEvent(
    eventType: AnalyticsEventType,
    chartType?: string,
    chartId?: string | number,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    if (!this.isEnabled) {
      console.log('Analytics tracking is disabled');
      return;
    }

    if (!this.validateEventData(eventType, metadata)) {
      console.log('Event data validation failed', { eventType, metadata });
      return;
    }

    // Create unique key for this event to prevent duplicates
    const eventKey = `analytics_${eventType}_${chartType}_${chartId}_${this.sessionId}`;
    const now = Date.now();

    // Check if this exact event was sent recently (within 5 seconds)
    const lastEventTime = sessionStorage.getItem(eventKey);
    if (lastEventTime) {
      const timeDiff = now - parseInt(lastEventTime);
      if (timeDiff < 5000) { // 5 seconds
        console.log('Duplicate analytics event prevented:', {
          eventType, chartType, chartId, timeDiff
        });
        return;
      }
    }

    // Mark this event as sent
    sessionStorage.setItem(eventKey, now.toString());

    try {
      console.log('Tracking analytics event:', {
        eventType, chartType, chartId, metadata
      });

      const headers = getHeaders();
      const chartData: ChartEvent = {
        event_type: eventType,
        chart_type: chartType || 'unknown',
        chart_id: chartId,
        session_id: this.sessionId,
        user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
        ip_address: getClientIPAddress()
      };

      console.log('Making API request to:', `${API_BASE_URL}/analytics/events/chart`);
      console.log('With data:', {...chartData, metadata});

      await this.retryRequest(
        () => fetchWithAuthRetry(
          `${API_BASE_URL}/analytics/events/chart`,
          {
            method: 'POST',
            headers,
            body: JSON.stringify({
              ...chartData,
              metadata: metadata // Ensure metadata is included in the request
            }),
          }
        ),
        `chart event: ${eventType} (${chartType})`
      );

      console.log('Analytics event sent successfully');
    } catch (error) {
      console.error('Analytics tracking failed for chart event:', eventType, error);
      // Remove the event marker on error so it can be retried
      sessionStorage.removeItem(eventKey);
    }
  }

  /**
   * Track page view events
   */
  public async trackPageView(
    pageTitle?: string
  ): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const headers = getHeaders();
      const pageUrl = typeof window !== 'undefined' ? window.location.href : '';
      const title = pageTitle || (typeof document !== 'undefined' ? document.title : '');

      // Validate page URL
      if (!pageUrl) {
        console.warn('Analytics: No page URL available for page view tracking');
        return;
      }

      // Create URL with query parameters as expected by backend
      const url = new URL(`${API_BASE_URL}/analytics/events/page-view`);


      const pageData: PageViewEvent = {
        page_title: title || 'Untitled Page',
        page_url: pageUrl,
        session_id: this.sessionId,
        user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
        referrer: typeof document !== 'undefined' ? document.referrer : '',
        ip_address: getClientIPAddress()
      };

      await this.retryRequest(
        () => fetchWithAuthRetry(
          url.toString(),
          {
            method: 'POST',
            headers,
            body: JSON.stringify(pageData),
          }
        ),
        `page view: ${title}`
      );
    } catch (error) {
      console.warn('Analytics tracking failed for page view:', error);
    }
  }

  /**
   * Get user profile analytics
   */
  public async getUserProfile(): Promise<any> {
    try {
      const headers = getHeaders();
      const response = await fetchWithAuthRetry(
        `${API_BASE_URL}/analytics/user-profile`,
        {
          method: 'GET',
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to get user profile: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting user profile:', error);
      throw error;
    }
  }

  /**
   * Check analytics service health
   */
  public async checkHealth(): Promise<any> {
    try {
      const headers = getHeaders();
      const response = await fetchWithAuthRetry(
        `${API_BASE_URL}=analytics/health`,
        {
          method: 'GET',
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`Analytics health check failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Analytics health check failed:', error);
      throw error;
    }
  }
}

// Export singleton instance for easy access
export const analytics = AnalyticsService.getInstance();
