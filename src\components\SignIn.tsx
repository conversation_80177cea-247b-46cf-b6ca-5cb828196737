import React, { useEffect } from "react";
import { Box, Typography, Paper } from "@mui/material";
import { GoogleOAuthProvider, GoogleLogin } from "@react-oauth/google";
import config from "../config";
import { googleLogin,initializeSession  } from "../services/apiService";

// Utility function for smooth scroll to top
const scrollToTop = (smooth: boolean = true) => {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: smooth ? 'smooth' : 'auto'
  });
};

interface SignInProps {
  onSignInSuccess: () => void;
}

const SignIn: React.FC<SignInProps> = ({ onSignInSuccess }) => {
  // Scroll to top when component mounts
  useEffect(() => {
    scrollToTop();
  }, []);

  const handleGoogleSuccess = async (credentialResponse: any) => {
    console.log("Google login success:", credentialResponse);

    initializeSession();

    const token = credentialResponse.credential;
    if (!token) {
      console.error("No token received from Google");
      return;
    }

    try {
      const response = await googleLogin(token);
      console.log("Backend auth success:", response);

      // Example: save tokens / user details
      localStorage.setItem("access_token", response.access_token);
      localStorage.setItem("refresh_token", response.refresh_token);

      onSignInSuccess();
    } catch (error) {
      console.error("Backend auth failed:", error);
    }
  };

  const handleGoogleFailure = () => {
    console.error("Google login failed");
  };

  return (
    <Box
      sx={{
        minHeight: "80vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#ffffff",
        p: 2,
      }}
    >
      <Paper
        elevation={0}
        sx={{
          p: 5,
          borderRadius: 3,
          maxWidth: 400,
          width: "100%",
          textAlign: "center",
          backgroundColor: "#ffffff",
          border: "1px solid rgba(0,0,0,0.08)",
          boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
        }}
      >
        <Typography
          variant="h3"
          gutterBottom
          sx={{
            fontWeight: 600,
            color: "#1a1a1a",
            mb: 3,
            fontSize: { xs: "1.5rem", sm: "1.75rem" },
          }}
        >
          Become a Data Storyteller
        </Typography>

        <GoogleOAuthProvider clientId={config.googleClientId}>
          <GoogleLogin
            onSuccess={handleGoogleSuccess}
            onError={handleGoogleFailure}
          />
        </GoogleOAuthProvider>
      </Paper>
    </Box>
  );
};

export default SignIn;
