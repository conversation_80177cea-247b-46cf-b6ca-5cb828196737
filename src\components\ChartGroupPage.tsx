import React, { useEffect, useState } from 'react';
import { Box, Paper, Typography } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Lazy<PERSON>hart from './LazyChart';
import { ChartData } from './ChartGrid';
import ChartPreviewModal from './common/ChartPreviewModal';
import ProfessionalButton from './common/ProfessionalButton';

// Utility function for smooth scroll to top
const scrollToTop = (smooth: boolean = true) => {
  // First try to scroll the main content area
  const mainContentArea = document.querySelector('[data-main-content="true"]');
  if (mainContentArea) {
    mainContentArea.scrollTo({
      top: 0,
      left: 0,
      behavior: smooth ? 'smooth' : 'auto'
    });
  }
  // Also scroll window to top as fallback
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: smooth ? 'smooth' : 'auto'
  });
};

interface ChartGroupPageProps {
  groupName: string;
  charts: ChartData[];
  onBack: () => void;
  onChartUpdate: (index: number, updatedChart: ChartData) => void;
  onInsightsClick?: (chartData: ChartData) => void;
  onModifyClick?: (chartData: ChartData) => void;
  gridBackgroundColor: string;
}

const ChartGroupPage: React.FC<ChartGroupPageProps> = ({
  charts,
  onBack,
  onInsightsClick,
  onModifyClick,
}) => {
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [previewChartData, setPreviewChartData] = useState<ChartData | null>(null);

  // Scroll to top when component mounts
  useEffect(() => {
    scrollToTop();
  }, []);

  // Handle preview click
  const handlePreviewClick = (chartData: ChartData) => {
    setPreviewChartData(chartData);
    setPreviewModalOpen(true);
  };

  // Handle preview modal close
  const handlePreviewClose = () => {
    setPreviewModalOpen(false);
    setPreviewChartData(null);
  };

  // Responsive layout - no need to group into rows, let CSS Grid handle it

  return (
    <Box sx={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      p: { xs: 2, sm: 2.5, md: 3 },
      backgroundColor: 'white',
      minHeight: '100vh',
    }}>
      {/* Page Header - Back button on left, title in center, other buttons on right */}
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        mb: { xs: 2, md: 3 },
        gap: { xs: 2, sm: 0 },
      }}>
        {/* Left side - Back button */}
        <ProfessionalButton
          variant="primary"
          startIcon={<ArrowBackIcon />}
          onClick={onBack}
          sx={{
            fontSize: { xs: '0.9rem', md: '1rem' },
            px: { xs: 2, md: 3 },
            py: { xs: 1, md: 1.2 },
            alignSelf: { xs: 'flex-start', sm: 'auto' },
          }}
        >
          Back
        </ProfessionalButton>

        {/* Center - Page title and subtitle */}
        <Box sx={{
          textAlign: { xs: 'left', sm: 'center' },
          flex: 1,
          mx: { xs: 0, sm: 2 },
          order: { xs: -1, sm: 0 }
        }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 'bold',
              color: 'text.primary',
              mb: 1,
              fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' },
            }}
          >
            Additional Charts
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: 'text.secondary',
              fontSize: { xs: '0.9rem', md: '1rem' },
            }}
          >
            {charts.length} similar chart{charts.length !== 1 ? 's' : ''} generated
          </Typography>
        </Box>

        {/* Right side - Placeholder for future buttons */}
        <Box sx={{
          width: { xs: '0px', sm: '80px' },
          display: { xs: 'none', sm: 'block' }
        }}></Box>
      </Box>

      {/* Charts Section */}
      <Box sx={{
        width: '100%',
        backgroundColor: 'white',
        p: { xs: 2, sm: 2.5, md: 3 },
        flexGrow: 1,
        overflow: 'auto',
      }}>
        {/* Charts Grid - Responsive */}
        <Box sx={{
          width: '100%',
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr', // Single column on mobile
            sm: '1fr', // Single column on small tablets
            md: 'repeat(2, 1fr)', // Two columns on medium screens and up
          },
          gap: 2,
          '@media (max-width: 900px)': {
            gridTemplateColumns: '1fr', // Force single column below 900px
          }
        }}>
          {charts.map((chart, chartIndex) => (
            chart && (
              <Paper
                key={`chart-${chartIndex}`}
                elevation={0}
                sx={{
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  borderRadius: 2,
                  overflow: 'hidden', // Prevent horizontal scrollbars
                  width: '100%', // Full width in grid
                  aspectRatio: '1.4/1', // Increased aspect ratio for larger chart image
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
                  p: 0.5, // Further reduced padding for more chart space
                }}
              >
                <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0 }}>
                  <LazyChart
                    data={chart}
                    position={chartIndex % 2 === 0 ? "left" : "right"} // Alternate position based on index
                    width="100%"
                    height="100%"
                    loadImmediately={chartIndex < 2} // Load first 2 charts immediately
                    delay={chartIndex * 150} // Stagger loading with delays
                    onInsightsClick={onInsightsClick}
                    onPreviewClick={handlePreviewClick}
                    onModifyClick={onModifyClick}
                  />
                </Box>
              </Paper>
            )
          ))}
        </Box>
      </Box>

      {/* Chart Preview Modal */}
      <ChartPreviewModal
        open={previewModalOpen}
        onClose={handlePreviewClose}
        chartData={previewChartData}
      />
    </Box>
  );
};

export default ChartGroupPage;
