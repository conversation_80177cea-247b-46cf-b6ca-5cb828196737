import React from 'react';
import { Box, Typography, Alert, AlertTitle } from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import RefreshIcon from '@mui/icons-material/Refresh';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ProfessionalButton from './ProfessionalButton';
import { ApiError } from '../../services/apiService';

interface ErrorDisplayProps {
  error: string | ApiError;
  onRetry?: () => void;
  onGoBack?: () => void;
  variant?: 'inline' | 'page' | 'snackbar';
  title?: string;
  showActions?: boolean;
  retryLabel?: string;
  goBackLabel?: string;
  className?: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  onGoBack,
  variant = 'page',
  title,
  showActions = true,
  retryLabel = 'Try Again',
  goBackLabel = 'Go Back',
  className
}) => {
  // Extract error details
  const errorMessage = typeof error === 'string' ? error : error.message;
  const errorType = typeof error === 'string' ? 'error' : error.type;
  const isWarning = errorType === 'warning';

  // Get appropriate icon and colors
  const icon = isWarning ? <WarningAmberIcon /> : <ErrorOutlineIcon />;
  const iconColor = isWarning ? '#ff9800' : '#e53e3e';
  const bgColor = isWarning ? '#fff8e1' : '#fff5f5';
  const borderColor = isWarning ? '#ffcc02' : '#fed7d7';
  const textColor = isWarning ? '#e65100' : '#e53e3e';

  // Default titles
  const defaultTitle = isWarning 
    ? 'Warning' 
    : 'Something went wrong';

  const displayTitle = title || defaultTitle;

  // Inline variant (for small spaces)
  if (variant === 'inline') {
    return (
      <Alert 
        severity={isWarning ? 'warning' : 'error'}
        className={className}
        sx={{
          '& .MuiAlert-message': {
            width: '100%'
          }
        }}
        action={
          showActions && (onRetry || onGoBack) ? (
            <Box sx={{ display: 'flex', gap: 1 }}>
              {onRetry && (
                <ProfessionalButton
                  size="small"
                  variant="outlined"
                  onClick={onRetry}
                  startIcon={<RefreshIcon />}
                  sx={{ 
                    fontSize: '0.75rem',
                    py: 0.5,
                    px: 1,
                    borderColor: textColor,
                    color: textColor,
                    '&:hover': {
                      borderColor: textColor,
                      backgroundColor: `${textColor}10`
                    }
                  }}
                >
                  {retryLabel}
                </ProfessionalButton>
              )}
              {onGoBack && (
                <ProfessionalButton
                  size="small"
                  variant="outlined"
                  onClick={onGoBack}
                  startIcon={<ArrowBackIcon />}
                  sx={{ 
                    fontSize: '0.75rem',
                    py: 0.5,
                    px: 1,
                    borderColor: '#718096',
                    color: '#718096',
                    '&:hover': {
                      borderColor: '#4a5568',
                      backgroundColor: '#f7fafc'
                    }
                  }}
                >
                  {goBackLabel}
                </ProfessionalButton>
              )}
            </Box>
          ) : undefined
        }
      >
        <AlertTitle sx={{ fontSize: '0.875rem', fontWeight: 600 }}>
          {displayTitle}
        </AlertTitle>
        <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
          {errorMessage}
        </Typography>
      </Alert>
    );
  }

  // Snackbar variant (for notifications)
  if (variant === 'snackbar') {
    return (
      <Alert
        severity={isWarning ? 'warning' : 'error'}
        variant="filled"
        className={className}
        sx={{
          fontSize: '1rem',
          fontWeight: 500,
          backgroundColor: isWarning ? '#ff9800' : '#f44336',
          '& .MuiAlert-message': {
            display: 'flex',
            alignItems: 'center',
          },
          '& .MuiAlert-icon': {
            color: 'white',
          }
        }}
      >
        {errorMessage}
      </Alert>
    );
  }

  // Page variant (full page error display)
  return (
    <Box 
      className={className}
      sx={{ 
        width: '100%', 
        height: '100%',
        minHeight: '400px',
        display: 'flex', 
        flexDirection: 'column', 
        justifyContent: 'center', 
        alignItems: 'center', 
        p: 3 
      }}
    >
      <Box sx={{ 
        textAlign: 'center', 
        maxWidth: 600, 
        p: 4, 
        borderRadius: 3, 
        backgroundColor: bgColor, 
        border: `1px solid ${borderColor}`,
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
      }}>
        {/* Error Icon */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          mb: 2 
        }}>
          <Box sx={{ 
            color: iconColor, 
            fontSize: '3rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            {icon}
          </Box>
        </Box>

        {/* Error Title */}
        <Typography variant="h5" sx={{ 
          color: textColor, 
          mb: 2, 
          fontWeight: 600 
        }}>
          {displayTitle}
        </Typography>

        {/* Error Message */}
        <Typography variant="body1" sx={{ 
          color: '#2d3748', 
          mb: showActions && (onRetry || onGoBack) ? 3 : 0, 
          lineHeight: 1.6 
        }}>
          {errorMessage}
        </Typography>

        {/* Action Buttons */}
        {showActions && (onRetry || onGoBack) && (
          <Box sx={{ 
            display: 'flex', 
            gap: 2, 
            justifyContent: 'center', 
            flexWrap: 'wrap' 
          }}>
            {onRetry && (
              <ProfessionalButton 
                variant="outlined" 
                onClick={onRetry}
                startIcon={<RefreshIcon />}
                sx={{ 
                  borderColor: textColor, 
                  color: textColor,
                  '&:hover': { 
                    borderColor: textColor, 
                    backgroundColor: `${textColor}10`
                  }
                }}
              >
                {retryLabel}
              </ProfessionalButton>
            )}
            {onGoBack && (
              <ProfessionalButton 
                variant="outlined" 
                onClick={onGoBack}
                startIcon={<ArrowBackIcon />}
                sx={{ 
                  borderColor: '#718096', 
                  color: '#718096',
                  '&:hover': { 
                    borderColor: '#4a5568', 
                    color: '#4a5568',
                    backgroundColor: '#f7fafc'
                  }
                }}
              >
                {goBackLabel}
              </ProfessionalButton>
            )}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default ErrorDisplay;
