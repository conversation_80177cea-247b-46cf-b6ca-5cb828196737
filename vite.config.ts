import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// Plugin to completely block MUI icons from being processed
const blockMuiIcons = () => ({
  name: 'block-mui-icons',
  resolveId(id: string) {
    if (id.includes('@mui/icons-material')) {
      return false; // Block resolution
    }
  },
  load(id: string) {
    if (id.includes('@mui/icons-material')) {
      return 'export default {}'; // Return empty export
    }
  }
});

// Configuration optimized for Windows file system limits
export default defineConfig({
  plugins: [
    react({
      fastRefresh: false, // Disable React Refresh
    }),
    blockMuiIcons() // Block MUI icons completely
  ],
  server: {
    port: 5173,
    host: 'localhost',
    hmr: false, // Disable HMR completely
    watch: {
      // Reduce file watching to prevent EMFILE errors
      usePolling: false,
      interval: 1000,
      binaryInterval: 2000,
      ignored: [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/build/**',
        '**/.vscode/**',
        '**/.idea/**',
        '**/coverage/**',
        '**/*.log',
        '**/.env*',
        '**/package-lock.json',
        '**/yarn.lock'
      ]
    }
  },
  resolve: {
    dedupe: ['react', 'react-dom', '@emotion/react', '@emotion/styled'],
    alias: {
      // Redirect all MUI icon imports to our proxy to prevent EMFILE errors
      '@mui/icons-material': '/src/utils/muiIconsProxy.ts',
      '@mui/icons-material/*': '/src/utils/muiIconsProxy.ts'
    }
  },
  // Aggressively optimize dependencies and reduce file handles
  optimizeDeps: {
    exclude: [
      '@mui/icons-material',
      '@mui/icons-material/*',
      '@mui/icons-material/**/*'
    ],
    include: [
      'react',
      'react-dom',
      '@mui/material',
      '@emotion/react',
      '@emotion/styled',
      'plotly.js-dist-min'
    ],
    // Force dependency optimization to reduce file handles
    force: true,
    // Completely ignore MUI icons during dependency scanning
    entries: [
      'src/**/*.{ts,tsx}',
      '!src/**/*.d.ts',
      '!node_modules/@mui/icons-material/**/*'
    ]
  },
  // Build optimizations to reduce file system pressure
  build: {
    rollupOptions: {
      external: [
        // Treat MUI icons as external to prevent bundling
        /@mui\/icons-material\/.*/
      ],
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@emotion/react', '@emotion/styled'],
          plotly: ['plotly.js-dist-min']
        },
        // Handle external MUI icons
        globals: {
          '@mui/icons-material': 'MuiIcons'
        }
      }
    },
    // Reduce concurrent file operations
    chunkSizeWarningLimit: 1000
  },
  // Reduce file system operations
  esbuild: {
    logOverride: { 'this-is-undefined-in-esm': 'silent' }
  }
})
