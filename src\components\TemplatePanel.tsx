import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import ColorPicker from './ColorPicker'
interface TemplatePanelProps {
  backgroundColor: string;
  onBackgroundColorChange: (color: string) => void;
}

const TemplatePanel: React.FC<TemplatePanelProps> = ({ 
  backgroundColor, 
  onBackgroundColorChange 
}) => {
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" sx={{ mb: 2 }}>Slide Settings</Typography>
      
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Slide Dimensions
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Standard PPT Size (16:9)
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Width: 1920px • Height: 1080px
        </Typography>
      </Paper>

      <ColorPicker 
        value={backgroundColor}
        onChange={onBackgroundColorChange}
      />
    </Box>
  );
};

export default TemplatePanel;
