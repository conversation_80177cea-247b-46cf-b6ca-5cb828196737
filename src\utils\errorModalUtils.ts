import { ErrorModalProps } from '../components/common/ErrorModal';

/**
 * Generate a unique error ID
 */
export function generateErrorId(): string {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 8);
  return `error_${timestamp}_${randomPart}`;
}

/**
 * Extract error details from various error types
 */
export function extractErrorDetails(error: any): {
  message: string;
  stack?: string;
  name?: string;
  status?: number;
  code?: string;
} {
  if (error instanceof Error) {
    return {
      message: error.message,
      stack: error.stack,
      name: error.name,
      status: (error as any).status,
      code: (error as any).code
    };
  }

  if (typeof error === 'string') {
    return { message: error };
  }

  if (error && typeof error === 'object') {
    return {
      message: error.message || error.error || 'Unknown error',
      stack: error.stack,
      name: error.name,
      status: error.status,
      code: error.code
    };
  }

  return { message: 'Unknown error occurred' };
}

/**
 * Get user-friendly error title and description based on error type
 */
export function getErrorDisplayInfo(error: any): {
  title: string;
  description: string;
} {
  const errorDetails = extractErrorDetails(error);
  const message = errorDetails.message.toLowerCase();
  const status = errorDetails.status;

  // Network errors
  if (message.includes('network') || message.includes('fetch')) {
    return {
      title: 'Connection Problem',
      description: 'Unable to connect to our servers. Please check your internet connection and try again.'
    };
  }

  // Timeout errors
  if (message.includes('timeout') || message.includes('aborted')) {
    return {
      title: 'Request Timeout',
      description: 'The request is taking longer than expected. This might be due to a slow internet connection or server processing time.'
    };
  }

  // Authentication errors
  if (status === 401 || message.includes('unauthorized') || message.includes('authentication')) {
    return {
      title: 'Authentication Required',
      description: 'Your session has expired or you need to sign in to access this feature.'
    };
  }

  // Permission errors
  if (status === 403 || message.includes('forbidden') || message.includes('permission')) {
    return {
      title: 'Access Denied',
      description: 'You do not have permission to perform this action. Please contact support if you believe this is an error.'
    };
  }

  // Not found errors
  if (status === 404 || message.includes('not found')) {
    return {
      title: 'Resource Not Found',
      description: 'The requested resource could not be found. It may have been moved or deleted.'
    };
  }

  // Rate limiting
  if (status === 429 || message.includes('rate limit') || message.includes('too many requests')) {
    return {
      title: 'Too Many Requests',
      description: 'You have made too many requests in a short time. Please wait a moment and try again.'
    };
  }

  // Server errors
  if (status && status >= 500) {
    return {
      title: 'Server Error',
      description: 'Our servers are experiencing issues. Our team has been notified and is working to fix this problem.'
    };
  }

  // File upload errors
  if (message.includes('upload') || message.includes('file')) {
    return {
      title: 'Upload Failed',
      description: 'There was a problem uploading your file. Please check the file format and size, then try again.'
    };
  }

  // Chart processing errors
  if (message.includes('chart') || message.includes('visualization')) {
    return {
      title: 'Chart Processing Error',
      description: 'There was an issue processing your chart data. Please verify your data format and try again.'
    };
  }

  // Validation errors
  if (status === 400 || message.includes('validation') || message.includes('invalid')) {
    return {
      title: 'Invalid Data',
      description: 'The provided data is invalid or incomplete. Please check your input and try again.'
    };
  }

  // Default error
  return {
    title: 'Something went wrong',
    description: 'An unexpected error occurred. Our team has been notified and is working to fix this issue.'
  };
}

/**
 * Create error modal props from an error object
 */
export function createErrorModalProps(
  error: any,
  options: {
    onRetry?: () => void;
    onBack?: () => void;
    retryLabel?: string;
    backLabel?: string;
    customTitle?: string;
    customDescription?: string;
    context?: string;
  } = {}
): Omit<ErrorModalProps, 'open' | 'onClose'> {
  const errorDetails = extractErrorDetails(error);
  const { title, description } = getErrorDisplayInfo(error);
  const errorId = generateErrorId();

  return {
    title: options.customTitle || title,
    description: options.customDescription || description,
    errorId,
    technicalDetails: {
      error: errorDetails.message,
      stack: errorDetails.stack,
      context: options.context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    },
    onRetry: options.onRetry,
    onBack: options.onBack,
    retryLabel: options.retryLabel,
    backLabel: options.backLabel
  };
}

/**
 * Show error modal with automatic error handling
 */
export function showErrorModal(
  error: any,
  setModalProps: (props: ErrorModalProps) => void,
  options: Parameters<typeof createErrorModalProps>[1] = {}
) {
  const props = createErrorModalProps(error, options);
  setModalProps({
    ...props,
    open: true,
    onClose: () => setModalProps(prev => ({ ...prev, open: false }))
  });
}

/**
 * Error types for better categorization
 */
export enum ErrorCategory {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  PERMISSION = 'permission',
  VALIDATION = 'validation',
  SERVER = 'server',
  TIMEOUT = 'timeout',
  UPLOAD = 'upload',
  CHART = 'chart',
  UNKNOWN = 'unknown'
}

/**
 * Categorize error for analytics or logging
 */
export function categorizeError(error: any): ErrorCategory {
  const errorDetails = extractErrorDetails(error);
  const message = errorDetails.message.toLowerCase();
  const status = errorDetails.status;

  if (message.includes('network') || message.includes('fetch')) {
    return ErrorCategory.NETWORK;
  }
  if (message.includes('timeout') || message.includes('aborted')) {
    return ErrorCategory.TIMEOUT;
  }
  if (status === 401 || message.includes('unauthorized')) {
    return ErrorCategory.AUTHENTICATION;
  }
  if (status === 403 || message.includes('forbidden')) {
    return ErrorCategory.PERMISSION;
  }
  if (status === 400 || message.includes('validation')) {
    return ErrorCategory.VALIDATION;
  }
  if (status && status >= 500) {
    return ErrorCategory.SERVER;
  }
  if (message.includes('upload') || message.includes('file')) {
    return ErrorCategory.UPLOAD;
  }
  if (message.includes('chart') || message.includes('visualization')) {
    return ErrorCategory.CHART;
  }

  return ErrorCategory.UNKNOWN;
}
