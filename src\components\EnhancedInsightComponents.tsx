import React, { useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Fade
} from '@mui/material';
import Plot from 'react-plotly.js';

// Modern Insight Panel Component - Clean design without boxes
interface ModernInsightPanelProps {
  title: string;
  insights: string[];
  color: string;
  icon: string;
}

export const ModernInsightPanel: React.FC<ModernInsightPanelProps> = ({ 
  title, 
  insights, 
  color, 
  icon 
}) => {
  return (
    <Fade in timeout={800}>
      <Paper
        elevation={2}
        sx={{
          height: '100%',
          p: 3,
          borderRadius: 3,
          background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
          border: `2px solid ${color}20`,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: `0 12px 24px ${color}15`,
            transition: 'all 0.3s ease'
          }
        }}
      >
        {/* Header */}
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          mb: 2,
          pb: 2,
          borderBottom: `1px solid ${color}20`
        }}>
          <Box sx={{ 
            fontSize: '1.5rem',
            mr: 2,
            p: 1,
            borderRadius: '50%',
            backgroundColor: `${color}10`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minWidth: 40,
            height: 40
          }}>
            {icon}
          </Box>
          <Typography 
            variant="h6" 
            sx={{ 
              fontWeight: 600,
              color: color,
              fontSize: '1.1rem'
            }}
          >
            {title}
          </Typography>
        </Box>

        {/* Insights List - Clean bullet points */}
        <Box sx={{ 
          flexGrow: 1, 
          overflow: 'auto',
          '&::-webkit-scrollbar': {
            width: '4px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: color,
            borderRadius: '4px',
            opacity: 0.3
          }
        }}>
          {insights.slice(0, 5).map((insight, index) => (
            <Fade key={index} in timeout={1000 + index * 200}>
              <Box sx={{ 
                mb: 2,
                display: 'flex',
                alignItems: 'flex-start',
                '&:hover': {
                  backgroundColor: `${color}05`,
                  borderRadius: 1,
                  p: 1,
                  ml: -1,
                  mr: -1
                }
              }}>
                <Box sx={{
                  width: 6,
                  height: 6,
                  borderRadius: '50%',
                  backgroundColor: color,
                  mt: 1,
                  mr: 2,
                  flexShrink: 0
                }} />
                <Typography 
                  variant="body2"
                  sx={{ 
                    lineHeight: 1.5,
                    color: '#2c3e50',
                    fontSize: '0.9rem'
                  }}
                >
                  {insight}
                </Typography>
              </Box>
            </Fade>
          ))}
        </Box>

        {/* Decorative accent */}
        <Box sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: 40,
          height: 40,
          background: `linear-gradient(135deg, ${color}15 0%, transparent 70%)`,
          borderBottomLeftRadius: '100%'
        }} />
      </Paper>
    </Fade>
  );
};

// Compact Insight Card for Mobile
interface CompactInsightCardProps {
  title: string;
  insights: string[];
  color: string;
}

export const CompactInsightCard: React.FC<CompactInsightCardProps> = ({ 
  title, 
  insights, 
  color 
}) => {
  return (
    <Paper
      elevation={1}
      sx={{
        p: 2,
        borderRadius: 2,
        background: 'white',
        border: `1px solid ${color}30`,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        height: '100%'
      }}
    >
      <Typography 
        variant="subtitle2" 
        sx={{ 
          fontWeight: 600,
          color: color,
          mb: 1,
          fontSize: '0.85rem'
        }}
      >
        {title}
      </Typography>
      
      <Box sx={{ 
        flexGrow: 1, 
        overflow: 'auto',
        '&::-webkit-scrollbar': { width: '2px' },
        '&::-webkit-scrollbar-thumb': { background: color, borderRadius: '2px' }
      }}>
        {insights.slice(0, 3).map((insight, index) => (
          <Typography 
            key={index}
            variant="caption"
            sx={{ 
              display: 'block',
              lineHeight: 1.3,
              color: '#2c3e50',
              mb: 0.5,
              fontSize: '0.75rem'
            }}
          >
            • {insight.length > 80 ? `${insight.substring(0, 80)}...` : insight}
          </Typography>
        ))}
      </Box>
    </Paper>
  );
};

// Clean Chart Display Component - Same size as ChartsPage but without action buttons
interface CleanChartDisplayProps {
  chartData: any;
}

export const CleanChartDisplay: React.FC<CleanChartDisplayProps> = ({ chartData }) => {
  const plotRef = useRef<any>(null);

  // Prepare chart data for display (same logic as Chart component)
  const prepareData = () => {
    const { data: rawData, chart_type } = chartData;
    let plotData = Array.isArray(rawData) ? rawData : [rawData];

    // Handle different chart types (same logic as original Chart component)
    if (chart_type === 'pie' || chart_type === 'donut') {
      return plotData.map(trace => ({
        ...trace,
        type: 'pie',
        labels: trace.labels || trace.x,
        values: trace.values || trace.y,
        hole: chart_type === 'donut' ? 0.4 : 0,
        textinfo: 'percent', // Show only percentages, categories shown in legend
        textposition: 'auto',
        marker: {
          line: { width: 2, color: 'white' }
        }
      }));
    }

    if (chart_type === 'bar') {
      return plotData.map(trace => ({
        ...trace,
        type: 'bar',
        text: trace.y || trace.values,
        textposition: 'outside',
        marker: {
          color: '#3498db',
          line: { width: 1, color: 'white' }
        }
      }));
    }

    if (chart_type === 'line') {
      return plotData.map(trace => ({
        ...trace,
        type: 'scatter',
        mode: 'lines+markers',
        line: { width: 3, color: '#e74c3c' },
        marker: { size: 8, color: '#e74c3c' }
      }));
    }

    return plotData;
  };

  const getLayout = () => ({
    title: {
      text: chartData.layout?.title || 'Chart',
      font: { size: 18, color: '#2c3e50', family: 'Inter, Arial, sans-serif' },
      x: 0.5
    },
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
    margin: { t: 60, r: 40, b: 60, l: 60 },
    font: { color: '#2c3e50', family: 'Inter, Arial, sans-serif' },
    xaxis: {
      title: chartData.layout?.xaxis?.title || '',
      gridcolor: '#ecf0f1',
      linecolor: '#bdc3c7'
    },
    yaxis: {
      title: chartData.layout?.yaxis?.title || '',
      gridcolor: '#ecf0f1',
      linecolor: '#bdc3c7'
    },
    showlegend: false
  });

  return (
    <Box sx={{ 
      width: '100%', 
      height: '100%',
      position: 'relative',
      minHeight: 300
    }}>
      <Plot
        ref={plotRef}
        data={prepareData()}
        layout={getLayout()}
        useResizeHandler={true}
        style={{ width: '100%', height: '100%' }}
        config={{
          responsive: true,
          displayModeBar: false,
          displaylogo: false,
        }}
      />
    </Box>
  );
};
