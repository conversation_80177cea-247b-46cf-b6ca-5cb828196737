import React from 'react';
import {
  Dialog,
  DialogContent,
  Box,
  Typography,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Divider
} from '@mui/material';
import {
  Warning as WarningIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  ArrowBack as ArrowBackIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import ProfessionalButton from './ProfessionalButton';
import config from '../../config';

export interface ErrorModalProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  errorId?: string;
  technicalDetails?: {
    error?: string;
    stack?: string;
    context?: string;
    timestamp?: string;
    userAgent?: string;
    url?: string;
  };
  onRetry?: () => void;
  onBack?: () => void;
  retryLabel?: string;
  backLabel?: string;
  showTechnicalDetails?: boolean;
}

const ErrorModal: React.FC<ErrorModalProps> = ({
  open,
  onClose,
  title = 'Something went wrong',
  description = 'An unexpected error occurred. Our team has been notified and is working to fix this issue.',
  errorId,
  technicalDetails,
  onRetry,
  onBack,
  retryLabel = 'Try Again',
  backLabel = 'Back',
  showTechnicalDetails = config.showTechnicalDetails
}) => {
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
    onClose();
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
    onClose();
  };

  const formatTimestamp = (timestamp?: string) => {
    if (!timestamp) return new Date().toLocaleString();
    return new Date(timestamp).toLocaleString();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: 'fit-content',
          margin: 1,
          backgroundColor: '#ffffff',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.12)',
          maxWidth: '400px',
          overflow: 'visible'
        }
      }}
    >
      <DialogContent sx={{ p: 0, position: 'relative', overflow: 'visible' }}>
        {/* Close Button */}
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            zIndex: 10,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            width: 28,
            height: 28,
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 1)',
            }
          }}
        >
          <CloseIcon sx={{ fontSize: '1rem' }} />
        </IconButton>

        {/* Main Content */}
        <Box sx={{ p: 2, textAlign: 'center', overflow: 'visible' }}>
          {/* Warning Icon - Fully Visible */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            mb: 2
          }}>
            <Box sx={{
              width: 50,
              height: 50,
              borderRadius: '50%',
              backgroundColor: '#fee2e2',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '2px solid #fecaca'
            }}>
              <WarningIcon sx={{
                fontSize: '1.75rem',
                color: '#dc2626'
              }} />
            </Box>
          </Box>

          {/* Title */}
          <Typography variant="h5" sx={{
            color: '#dc2626',
            mb: 1.5,
            fontWeight: 600,
            fontSize: '1.25rem'
          }}>
            {title}
          </Typography>

          {/* Description */}
          <Typography variant="body2" sx={{
            color: '#374151',
            mb: 2,
            lineHeight: 1.5,
            fontSize: '0.875rem',
            maxWidth: '350px',
            mx: 'auto'
          }}>
            {description}
          </Typography>

          {/* Error ID Section */}
          {errorId && (
            <Box sx={{
              backgroundColor: '#dbeafe',
              borderRadius: 1.5,
              p: 2,
              mb: 2,
              border: '1px solid #bfdbfe'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
                <InfoIcon sx={{ color: '#2563eb', fontSize: '1rem' }} />
                <Typography variant="subtitle2" sx={{
                  color: '#1e40af',
                  fontWeight: 600,
                  fontSize: '0.875rem'
                }}>
                  Error ID
                </Typography>
              </Box>
              <Typography variant="body2" sx={{
                color: '#1e40af',
                fontFamily: 'monospace',
                fontSize: '0.75rem',
                wordBreak: 'break-all',
                mb: 0.5
              }}>
                {errorId}
              </Typography>
              <Typography variant="caption" sx={{
                color: '#64748b',
                fontSize: '0.6875rem'
              }}>
                Please include this ID when contacting support.
              </Typography>
            </Box>
          )}

          {/* Action Buttons */}
          <Box sx={{
            display: 'flex',
            gap: 1.5,
            justifyContent: 'center',
            flexWrap: 'wrap',
            mb: showTechnicalDetails && technicalDetails ? 2 : 0
          }}>
            {onRetry && (
              <ProfessionalButton
                variant="contained"
                onClick={handleRetry}
                startIcon={<RefreshIcon sx={{ fontSize: '0.875rem' }} />}
                size="small"
                sx={{
                  backgroundColor: '#2563eb',
                  fontSize: '0.8125rem',
                  px: 2,
                  py: 0.75,
                  '&:hover': {
                    backgroundColor: '#1d4ed8'
                  }
                }}
              >
                {retryLabel}
              </ProfessionalButton>
            )}

            {onBack && (
              <ProfessionalButton
                variant="outlined"
                onClick={handleBack}
                startIcon={<ArrowBackIcon sx={{ fontSize: '0.875rem' }} />}
                size="small"
                sx={{
                  borderColor: '#6b7280',
                  color: '#6b7280',
                  fontSize: '0.8125rem',
                  px: 2,
                  py: 0.75,
                  '&:hover': {
                    borderColor: '#4b5563',
                    color: '#4b5563',
                    backgroundColor: 'rgba(107, 114, 128, 0.04)'
                  }
                }}
              >
                {backLabel}
              </ProfessionalButton>
            )}
          </Box>

          {/* Technical Details Section */}
          {showTechnicalDetails && technicalDetails && (
            <Box sx={{ textAlign: 'left' }}>
              <Divider sx={{ mb: 1.5 }} />
              <Accordion
                elevation={0}
                sx={{
                  backgroundColor: '#f9fafb',
                  border: '1px solid #e5e7eb',
                  borderRadius: 1.5,
                  overflow: 'visible',
                  '&:before': {
                    display: 'none',
                  }
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon sx={{ fontSize: '1rem' }} />}
                  sx={{
                    backgroundColor: '#f3f4f6',
                    borderRadius: '6px 6px 0 0',
                    minHeight: 40,
                    '& .MuiAccordionSummary-content': {
                      alignItems: 'center',
                      margin: '8px 0'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <WarningIcon sx={{ color: '#d97706', fontSize: '1rem' }} />
                    <Typography variant="subtitle2" sx={{
                      fontWeight: 600,
                      color: '#92400e',
                      fontSize: '0.8125rem'
                    }}>
                      Technical Details (Development Only)
                    </Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails sx={{
                  p: 2,
                  maxHeight: '300px',
                  overflow: 'auto'
                }}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                    {/* Error Message */}
                    {technicalDetails.error && (
                      <Box>
                        <Typography variant="caption" sx={{
                          fontWeight: 600,
                          color: '#374151',
                          mb: 0.5,
                          fontSize: '0.75rem'
                        }}>
                          Error:
                        </Typography>
                        <Typography variant="body2" sx={{
                          fontFamily: 'monospace',
                          backgroundColor: '#fef2f2',
                          p: 1.5,
                          borderRadius: 1,
                          border: '1px solid #fecaca',
                          color: '#991b1b',
                          fontSize: '0.6875rem',
                          wordBreak: 'break-word',
                          overflow: 'visible'
                        }}>
                          {technicalDetails.error}
                        </Typography>
                      </Box>
                    )}

                    {/* Stack Trace */}
                    {technicalDetails.stack && (
                      <Box>
                        <Typography variant="caption" sx={{
                          fontWeight: 600,
                          color: '#374151',
                          mb: 0.5,
                          fontSize: '0.75rem'
                        }}>
                          Stack:
                        </Typography>
                        <Typography variant="body2" sx={{
                          fontFamily: 'monospace',
                          backgroundColor: '#f9fafb',
                          p: 1.5,
                          borderRadius: 1,
                          border: '1px solid #e5e7eb',
                          color: '#374151',
                          fontSize: '0.625rem',
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-word',
                          maxHeight: '150px',
                          overflow: 'auto'
                        }}>
                          {technicalDetails.stack}
                        </Typography>
                      </Box>
                    )}

                    {/* Additional Info */}
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {technicalDetails.context && (
                        <Chip
                          label={`Context: ${technicalDetails.context}`}
                          size="small"
                          variant="outlined"
                          sx={{
                            fontSize: '0.625rem',
                            height: 20
                          }}
                        />
                      )}
                      {technicalDetails.timestamp && (
                        <Chip
                          label={`Time: ${formatTimestamp(technicalDetails.timestamp)}`}
                          size="small"
                          variant="outlined"
                          sx={{
                            fontSize: '0.625rem',
                            height: 20
                          }}
                        />
                      )}
                      {technicalDetails.url && (
                        <Chip
                          label={`URL: ${technicalDetails.url}`}
                          size="small"
                          variant="outlined"
                          sx={{
                            fontSize: '0.625rem',
                            height: 20
                          }}
                        />
                      )}
                    </Box>
                  </Box>
                </AccordionDetails>
              </Accordion>
            </Box>
          )}
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default ErrorModal;
