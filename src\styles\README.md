# Design System Documentation

This document explains how to use the global design system implemented in this application.

## Overview

The design system provides consistent styling across all components with:

- **Font**: Manrope - rounded, clean, perfect for minimal UIs
- **Background**: #FFFFFF (Pure White)
- **Primary Text**: #2E2E2E (Neutral Dark)
- **Secondary Text**: #7A7A7A (Medium Gray)
- **Primary Accent**: #3B82F6 (Sky Blue)
- **Secondary Accent**: #10B981 (Emerald Green)

## Usage

### 1. Using Design Tokens

Import design tokens from the design system file:

```typescript
import { colors, typography, spacing, shadows } from '../styles/designSystem';

// Use in component styles
const MyComponent = () => (
  <Box sx={{
    backgroundColor: colors.background,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.primary,
    padding: spacing[4],
    boxShadow: shadows.md,
  }}>
    Content
  </Box>
);
```

### 2. Using Theme Colors

Import colors from the theme for Material-UI components:

```typescript
import { colors } from '../theme';

const MyButton = () => (
  <Button sx={{
    color: colors.textPrimary,
    backgroundColor: colors.accentPrimary,
    '&:hover': {
      backgroundColor: colors.accentSecondary,
    }
  }}>
    Click me
  </Button>
);
```

### 3. Material-UI Theme

The theme is automatically applied to all Material-UI components. No additional imports needed:

```typescript
// These automatically use the design system
<Typography variant="h1">Heading</Typography>
<Button variant="contained">Button</Button>
<Paper>Content</Paper>
```

## Color Palette

### Primary Colors
- **Background**: `#FFFFFF` - Main background color
- **Primary Text**: `#2E2E2E` - Main text, headings
- **Secondary Text**: `#7A7A7A` - Captions, labels, secondary content

### Accent Colors
- **Primary Accent**: `#3B82F6` - Primary buttons, links, active states
- **Secondary Accent**: `#10B981` - Success states, secondary actions

### Semantic Colors
- **Success**: `#10B981` (Emerald Green)
- **Error**: `#EF4444` (Red)
- **Warning**: `#F59E0B` (Amber)
- **Info**: `#3B82F6` (Sky Blue)

## Typography

### Font Family
- **Primary**: `"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica", "Arial", sans-serif`
- **Monospace**: `"SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Consolas", monospace`

### Font Sizes
- **xs**: 0.75rem (12px)
- **sm**: 0.875rem (14px)
- **base**: 1rem (16px)
- **lg**: 1.125rem (18px)
- **xl**: 1.25rem (20px)
- **2xl**: 1.5rem (24px)
- **3xl**: 1.875rem (30px)
- **4xl**: 2.25rem (36px)
- **5xl**: 3rem (48px)

## Spacing

Based on 4px grid system:
- **1**: 0.25rem (4px)
- **2**: 0.5rem (8px)
- **3**: 0.75rem (12px)
- **4**: 1rem (16px)
- **5**: 1.25rem (20px)
- **6**: 1.5rem (24px)
- **8**: 2rem (32px)
- **10**: 2.5rem (40px)
- **12**: 3rem (48px)
- **16**: 4rem (64px)
- **20**: 5rem (80px)
- **24**: 6rem (96px)
- **32**: 8rem (128px)

## Common Utilities

### Flexbox Utilities
```typescript
import { commonStyles } from '../styles/designSystem';

// Center content
<Box sx={commonStyles.flexCenter}>Content</Box>

// Space between items
<Box sx={commonStyles.flexBetween}>Content</Box>
```

### Text Utilities
```typescript
// Text ellipsis
<Typography sx={commonStyles.textEllipsis}>Long text...</Typography>
```

### Focus Styles
```typescript
// Consistent focus ring
<Button sx={commonStyles.focusRing}>Button</Button>
```

### Custom Scrollbar
```typescript
// Styled scrollbar
<Box sx={commonStyles.customScrollbar}>Scrollable content</Box>
```

## Best Practices

1. **Always use design tokens** instead of hardcoded values
2. **Import from the appropriate source**:
   - Use `designSystem.ts` for comprehensive tokens
   - Use `theme.ts` colors for quick Material-UI styling
3. **Maintain consistency** across all components
4. **Use semantic color names** (e.g., `colors.text.primary` instead of `#2E2E2E`)
5. **Follow the spacing grid** (multiples of 4px)
6. **Use the provided utilities** for common patterns

## Files

- `src/styles/designSystem.ts` - Complete design system tokens
- `src/theme.ts` - Material-UI theme configuration
- `src/index.css` - Global CSS with design system variables
