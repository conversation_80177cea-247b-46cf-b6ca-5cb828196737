import React from "react";
import { useDropzone } from "react-dropzone";
import { Box, Paper, Typography } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { uploadCSV, ApiError, TimeoutError } from '../services/apiService';
import { useApiErrorModal } from '../contexts/ErrorModalContext';

interface FileUploadProps {
  onDataProcessed: (data: any[]) => void;
  onError?: (error: ApiError) => void;
  onUploadStart?: () => void; // New callback to trigger progress steps immediately
}

const FileUpload: React.FC<FileUploadProps> = ({ onDataProcessed, onError, onUploadStart }) => {
  const { handleUploadError } = useApiErrorModal();
  const [file, setFile] = React.useState<File | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    multiple: false,
    onDrop: async (acceptedFiles) => {
      const selectedFile = acceptedFiles[0];
      setFile(selectedFile);
      if (selectedFile) {
        setIsLoading(true);
        try {
          await handleUpload(selectedFile);
        } finally {
          setIsLoading(false);
        }
      }
    }
  });

  const handleUpload = async (uploadFile: File | null = null) => {
    const fileToUpload = uploadFile || file;
    if (!fileToUpload) return;

    // Trigger progress steps immediately when upload starts
    onUploadStart?.();

    const formData = new FormData();
    formData.append('file', fileToUpload);
    try {
      const data = await uploadCSV(formData);

      // Check if the response contains an error
      if (data.error) {
        const apiError: ApiError = {
          message: "Our server is unable to process your request at the moment. Please try again later!",
          type: 'error'
        };
        handleUploadError(apiError, () => handleUpload(fileToUpload));
        onError?.(apiError);
        return;
      }

      // Check if charts data is valid
      if (data && Array.isArray(data.charts) && data.charts.length > 0) {
        const processedCharts = data.charts.map((chart: any) => {
          return {
            chart_type: chart.chart_type,
            chart_group: chart.chart_group, // Include chart_group field
            library: chart.library,
            data: chart.data,
            layout: chart.layout,
            allowed_chart_types: chart.allowed_chart_types // Include allowed_chart_types field
          };
        });
        onDataProcessed(processedCharts);
        setFile(null);
      } else {
        // Handle case where charts array is empty or invalid - this is a business logic issue
        const apiError: ApiError = {
          message: "No charts could be generated from your data. Please check your file format and try again.",
          type: 'warning'
        };
        handleUploadError(apiError, () => handleUpload(fileToUpload));
        onError?.(apiError);
      }
    } catch (error: any) {
      console.error('Error uploading file:', error);

      // Use the error modal for all errors
      handleUploadError(error, () => handleUpload(fileToUpload));

      // Still call the legacy onError callback for backward compatibility
      if (error && typeof error === 'object' && 'type' in error && 'message' in error) {
        onError?.(error as ApiError);
      } else if (error instanceof TimeoutError) {
        const apiError: ApiError = {
          message: error.message,
          type: 'error'
        };
        onError?.(apiError);
      } else {
        const apiError: ApiError = {
          message: "Unable to connect to our servers. Please check your internet connection and try again.",
          type: 'error'
        };
        onError?.(apiError);
      }
    }
  };

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      <Paper
        {...getRootProps()}
        sx={{
          width: '100%',
          height: '100%',
          minHeight: { xs: '250px', sm: '280px', md: '300px' },
          p: { xs: 2, sm: 3, md: 4 },
          border: '3px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.400',
          borderRadius: { xs: '12px', md: '16px' },
          backgroundColor: isDragActive ? 'primary.50' : 'white',
          cursor: isLoading ? 'default' : 'pointer',
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            borderColor: isLoading ? 'grey.400' : 'primary.main',
            backgroundColor: isLoading ? 'white' : 'primary.50',
          },
          opacity: isLoading ? 0.7 : 1,
          pointerEvents: isLoading ? 'none' : 'auto',
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: 'none',
        }}
      >
        <input {...getInputProps()} disabled={isLoading} />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: { xs: 2, sm: 2.5, md: 3 },
            textAlign: 'center',
            maxWidth: '100%',
          }}
        >
          <CloudUploadIcon
            sx={{
              fontSize: { xs: 60, sm: 70, md: 80 },
              color: isDragActive ? 'primary.main' : 'grey.500',
              transition: 'all 0.3s ease-in-out',
            }}
          />
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: { xs: 0.8, md: 1 },
            maxWidth: '100%'
          }}>
            <Typography
              variant="h6"
              align="center"
              sx={{
                fontWeight: 600,
                fontSize: { xs: '1rem', sm: '1.1rem', md: '1.25rem' }
              }}
            >
              {isLoading
                ? "Processing file..."
                : isDragActive
                  ? "Drop your file here"
                  : "Upload your data file"}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              align="center"
              sx={{
                fontSize: { xs: '0.8rem', sm: '0.85rem', md: '0.875rem' },
                px: { xs: 1, sm: 0 }
              }}
            >
              {isLoading
                ? "Please wait while we process your data..."
                : "Drag & drop CSV or Excel files, or click to select"}
            </Typography>
            <Typography
              variant="caption"
              color="text.secondary"
              align="center"
              sx={{
                fontSize: { xs: '0.7rem', sm: '0.75rem' }
              }}
            >
              Supported formats: .csv, .xlsx, .xls
            </Typography>
            {file && !isLoading && (
              <Typography
                variant="body2"
                color="primary.main"
                sx={{
                  mt: 1,
                  fontWeight: 500,
                  fontSize: { xs: '0.8rem', md: '0.875rem' },
                  wordBreak: 'break-all'
                }}
              >
                Selected: {file.name}
              </Typography>
            )}
          </Box>
        </Box>
      </Paper>

      {/* Error Modal is now handled globally by ErrorModalProvider */}
    </Box>
  );
};

export default FileUpload;
