# ErrorModal Component

A reusable, professional error modal component that provides consistent error handling across the application with dynamic content, retry functionality, and configurable technical details.

## Features

- ✅ **Fully visible warning icon** with professional styling
- ✅ **Dynamic title, description, and error ID** populated based on error type
- ✅ **Try Again and Back buttons** with customizable labels and actions
- ✅ **Configurable technical details** (shown in development, hidden in production)
- ✅ **Reusable modal** that can be displayed anywhere in the application
- ✅ **Automatic error categorization** and user-friendly messaging
- ✅ **Back navigation** that routes users to the original screen where error occurred

## Basic Usage

### 1. Using the Context Hook (Recommended)

```tsx
import { useApiErrorModal } from '../contexts/ErrorModalContext';

function MyComponent() {
  const { showError, handleApiError } = useApiErrorModal();

  const handleSomeAction = async () => {
    try {
      await someApiCall();
    } catch (error) {
      showError(error, {
        onRetry: () => handleSomeAction(),
        context: 'Data Loading'
      });
    }
  };

  return (
    <div>
      {/* Your component content */}
      {/* No need to render ErrorModal - it's handled globally */}
    </div>
  );
}
```

### 2. Direct Component Usage

```tsx
import ErrorModal from './common/ErrorModal';

function MyComponent() {
  const [errorModalOpen, setErrorModalOpen] = useState(false);

  return (
    <div>
      {/* Your component content */}
      <ErrorModal
        open={errorModalOpen}
        onClose={() => setErrorModalOpen(false)}
        title="Upload Failed"
        description="There was a problem uploading your file. Please check the file format and try again."
        errorId="error_1234567890_abc123"
        onRetry={() => {
          // Retry logic
          setErrorModalOpen(false);
        }}
        onBack={() => {
          // Back navigation logic
          setErrorModalOpen(false);
        }}
      />
    </div>
  );
}
```

## Configuration

### Environment Variables

Add to your `.env` file:

```env
# Show technical details in production (default: false, always true in development)
VITE_SHOW_TECHNICAL_DETAILS=false
```

### Technical Details Display

- **Development Mode**: Technical details are shown by default
- **Production Mode**: Technical details are hidden by default
- **Override**: Set `VITE_SHOW_TECHNICAL_DETAILS=true` to show in production

## API Reference

### ErrorModal Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `open` | `boolean` | - | Whether the modal is open |
| `onClose` | `() => void` | - | Callback when modal is closed |
| `title` | `string` | `'Something went wrong'` | Error title |
| `description` | `string` | `'An unexpected error occurred...'` | Error description |
| `errorId` | `string` | - | Unique error ID for support |
| `technicalDetails` | `object` | - | Technical error information |
| `onRetry` | `() => void` | - | Retry action callback |
| `onBack` | `() => void` | - | Back navigation callback |
| `retryLabel` | `string` | `'Try Again'` | Retry button label |
| `backLabel` | `string` | `'Back'` | Back button label |
| `showTechnicalDetails` | `boolean` | `config.showTechnicalDetails` | Show technical details section |

### useApiErrorModal Hook

```tsx
const {
  showError,           // Show error with automatic handling
  showCustomError,     // Show custom error modal
  closeError,          // Close the error modal
  isErrorOpen,         // Whether error modal is open
  handleApiError,      // Handle API errors with retry
  handleUploadError,   // Handle upload errors specifically
  handleChartError,    // Handle chart processing errors
  handleAuthError      // Handle authentication errors
} = useApiErrorModal();
```

### Setup (Required)

Add the ErrorModalProvider to your App.tsx:

```tsx
import { ErrorModalProvider } from './contexts/ErrorModalContext';

function App() {
  return (
    <ErrorModalProvider onNavigate={setCurrentPage}>
      {/* Your app content */}
    </ErrorModalProvider>
  );
}
```

## Error Types and Auto-Handling

The ErrorModal automatically categorizes errors and provides appropriate messaging:

### Network Errors
- Connection failures
- Timeout errors
- Fetch failures

### Authentication Errors (401)
- Token expired
- Invalid credentials
- Session timeout

### Permission Errors (403)
- Access denied
- Insufficient permissions

### Validation Errors (400)
- Invalid data format
- Missing required fields
- File format issues

### Server Errors (500+)
- Internal server errors
- Service unavailable
- Database errors

### Application-Specific Errors
- File upload failures
- Chart processing errors
- Data parsing errors

## Examples

### File Upload Error

```tsx
const handleFileUpload = async (file: File) => {
  try {
    await uploadFile(file);
  } catch (error) {
    handleUploadError(error, () => handleFileUpload(file));
  }
};
```

### API Call with Retry

```tsx
const fetchData = async () => {
  try {
    const data = await apiCall();
    setData(data);
  } catch (error) {
    handleApiError(error, fetchData, 'Data Fetching');
  }
};
```

### Custom Error Modal

```tsx
showCustomError({
  title: 'Custom Error',
  description: 'This is a custom error message.',
  errorId: 'custom_error_123',
  onRetry: () => console.log('Custom retry'),
  onBack: () => navigate('/dashboard'),
  retryLabel: 'Try Custom Action',
  backLabel: 'Go to Dashboard'
});
```

### Authentication Error

```tsx
const handleAuthError = (error: any) => {
  handleAuthError(error, () => {
    // Redirect to sign-in
    window.location.href = '/signin';
  });
};
```

## Integration with Existing Components

### FileUpload Component

The FileUpload component has been updated to use the ErrorModal:

```tsx
// Error handling is now done automatically
const { handleUploadError } = useApiErrorModal();

// In error handling
handleUploadError(error, () => handleUpload(file));

// No need to render ErrorModal - it's handled globally
```

### ErrorBoundary Component

The ErrorBoundary now uses ErrorModal for consistent error display:

```tsx
// Automatically shows ErrorModal for uncaught errors
// Provides retry and navigation options
// Shows technical details in development mode
```

## Best Practices

1. **Use the hook**: Prefer `useApiErrorModal` for consistent error handling
2. **Provide context**: Always include context information for better debugging
3. **Implement retry**: Provide retry functionality when possible
4. **Back navigation**: Implement proper back navigation to original screen
5. **Error categorization**: Let the system auto-categorize errors for consistent UX
6. **Technical details**: Keep technical details for development only
7. **User-friendly messages**: Focus on actionable, user-friendly error messages

## Styling

The ErrorModal uses the application's design system:

- **Colors**: Consistent with theme colors
- **Typography**: Uses Manrope font family
- **Spacing**: Follows design system spacing
- **Icons**: Material-UI icons with proper sizing
- **Responsive**: Works on all screen sizes

## Testing

Use the ErrorModalDemo component to test different error scenarios:

```tsx
import ErrorModalDemo from './components/ErrorModalDemo';

// Shows examples of all error types and configurations
```
