declare module 'react-plotly.js' {
  import { Component } from 'react';

  interface PlotParams {
    data: Array<{
      x?: any[];
      y?: any[];
      type?: string;
      mode?: string;
      name?: string;
      [key: string]: any;
    }>;
    layout?: {
      title?: string | { text: string };
      width?: number;
      height?: number;
      [key: string]: any;
    };
    frames?: any[];
    config?: any;
    onInitialized?: (figure: any) => void;
    onUpdate?: (figure: any) => void;
    onPurge?: (figure: any) => void;
    onError?: (err: Error) => void;
    onClick?: (event: any) => void;
    onClickAnnotation?: (event: any) => void;
    onHover?: (event: any) => void;
    onUnhover?: (event: any) => void;
    onSelected?: (event: any) => void;
    onDeselect?: (event: any) => void;
    onDoubleClick?: (event: any) => void;
    onRelayout?: (event: any) => void;
    onRestyle?: (event: any) => void;
    onRedraw?: (event: any) => void;
    [key: string]: any;
  }

  export default class Plot extends Component<PlotParams> {}
}