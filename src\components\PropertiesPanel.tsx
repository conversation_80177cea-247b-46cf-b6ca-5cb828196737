import React, { useState } from "react";
import { <PERSON>, Typography, Slider, TextField, Button, Paper, Divider } from "@mui/material";

interface PropertiesPanelProps {
  onUpdateProperties: (updatedProperties: any) => void;
}

const PropertiesPanel: React.FC<PropertiesPanelProps> = ({ onUpdateProperties }) => {
  const [chartTitle, setChartTitle] = useState<string>("");
  const [lineWidth, setLineWidth] = useState<number>(2);

  const handleTitleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChartTitle(event.target.value);
    onUpdateProperties({ title: event.target.value });
  };

  const handleLineWidthChange = (_event: Event, value: number | number[], _activeThumb: number) => {
    setLineWidth(value as number);
    onUpdateProperties({ lineWidth: value });
  };

  return (
    <Box sx={{ width: '100%', padding: 2 }}>
      <Typography variant="h6" sx={{ mb: 2 }}>Chart Properties</Typography>
      <Paper sx={{ padding: 2 }}>
        <TextField
          label="Chart Title"
          variant="outlined"
          value={chartTitle}
          onChange={handleTitleChange}
          fullWidth
          sx={{ mb: 2 }}
        />
        <Divider sx={{ mb: 2 }} />
        
        <Typography variant="body2" sx={{ mb: 1 }}>Line Width</Typography>
        <Slider
          value={lineWidth}
          min={1}
          max={10}
          step={1}
          onChange={handleLineWidthChange}
          valueLabelDisplay="auto"
          valueLabelFormat={(value) => `${value}px`}
        />
        
        <Divider sx={{ my: 2 }} />
        <Button
          variant="contained"
          color="primary"
          onClick={() => onUpdateProperties({ reset: true })}
          fullWidth
        >
          Reset Properties
        </Button>
      </Paper>
    </Box>
  );
};

export default PropertiesPanel;
