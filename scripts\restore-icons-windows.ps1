# PowerShell script to restore MUI icons after development

Write-Host "🔄 Restoring MUI icons..." -ForegroundColor Cyan

$muiIconsPath = "node_modules/@mui/icons-material"
$muiIconsBackupPath = "node_modules/@mui/icons-material.backup"

if (Test-Path $muiIconsBackupPath) {
    Write-Host "📦 Restoring original MUI icons..." -ForegroundColor Yellow
    
    # Remove the minimal replacement
    if (Test-Path $muiIconsPath) {
        Remove-Item -Recurse -Force $muiIconsPath
    }
    
    # Restore the original
    Move-Item $muiIconsBackupPath $muiIconsPath
    
    Write-Host "✅ MUI icons restored successfully!" -ForegroundColor Green
} else {
    Write-Host "⚠️  No backup found. MUI icons may already be restored." -ForegroundColor Yellow
}

Write-Host "🎉 Icons restoration complete!" -ForegroundColor Green
