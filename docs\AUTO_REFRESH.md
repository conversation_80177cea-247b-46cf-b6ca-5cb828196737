# Automatic Refresh & Cache Management

This application includes an automatic refresh system to ensure users always have the latest version without manual intervention.

## Features

### 🔄 Automatic Version Detection
- Checks for new versions every 3 minutes
- Compares build hash, version, and build time
- Detects updates when users return to the tab (focus event)

### 🚀 Smart Cache Management
- Service Worker handles cache invalidation
- Clears old cached files automatically
- Forces fresh downloads of updated assets

### 📢 User-Friendly Notifications
- Non-intrusive notification in top-right corner
- Users can choose when to refresh
- Auto-dismisses after 10 seconds if ignored
- Can be manually dismissed

### 🛠️ Developer Tools
- Build-time version injection
- Deployment scripts with automatic version bumping
- Cache manifest generation

## How It Works

### 1. Version Tracking
```typescript
// Environment variables injected at build time
VITE_APP_VERSION=1.0.0
VITE_BUILD_TIME=2024-12-23T10:00:00.000Z
VITE_BUILD_HASH=abc123def456
```

### 2. Version Check Hook
```typescript
const { hasUpdate, refreshApp } = useVersionCheck({
  checkInterval: 3 * 60 * 1000, // 3 minutes
  forceRefreshOnUpdate: false,   // Let user choose
  showUpdateNotification: true,
  enableAutoRefresh: true
});
```

### 3. Service Worker
- Caches static assets
- Clears old caches on activation
- Handles cache invalidation requests

## Usage

### For Developers

#### Deploy with Version Bump
```bash
npm run deploy:bump
```
This will:
1. Bump the patch version (1.0.0 → 1.0.1)
2. Update environment variables
3. Build the project
4. Generate cache manifest

#### Deploy without Version Bump
```bash
npm run deploy
```
This will:
1. Update build hash and time
2. Build the project
3. Generate cache manifest

#### Quick Deploy (Skip Build)
```bash
npm run deploy:quick
```
This will only update environment variables (useful for testing).

### For Users

#### Automatic Experience
1. User visits the application
2. Version checker runs in background
3. When new version is detected:
   - Notification appears in top-right
   - User can click "Refresh Now" or dismiss
   - If dismissed, notification reappears after 10 minutes

#### Manual Refresh
Users can always force refresh with `Ctrl+F5` or `Cmd+Shift+R`.

## Configuration

### Version Check Settings
```typescript
interface VersionCheckConfig {
  checkInterval?: number;        // Check frequency (default: 5 minutes)
  forceRefreshOnUpdate?: boolean; // Auto-refresh (default: true)
  showUpdateNotification?: boolean; // Show notifications (default: true)
  enableAutoRefresh?: boolean;   // Enable checking (default: true)
}
```

### Environment Variables
```bash
# Required for version tracking
VITE_APP_VERSION=1.0.0
VITE_BUILD_TIME=2024-12-23T10:00:00.000Z
VITE_BUILD_HASH=abc123def456

# Optional API configuration
VITE_API_TIMEOUT=30000
VITE_NODE_ENV=production
```

## Browser Support

### Service Worker
- Chrome 45+
- Firefox 44+
- Safari 11.1+
- Edge 17+

### Cache API
- Chrome 40+
- Firefox 39+
- Safari 11.1+
- Edge 16+

### Fallback Behavior
If Service Worker is not supported:
- Version checking still works
- Manual refresh required for cache clearing
- Notification system remains functional

## Troubleshooting

### Users Not Getting Updates
1. Check if Service Worker is registered:
   ```javascript
   navigator.serviceWorker.getRegistrations()
   ```

2. Verify version variables are updated:
   ```javascript
   console.log(import.meta.env.VITE_APP_VERSION)
   ```

3. Clear cache manually:
   ```javascript
   import { forceAppRefresh } from './hooks/useVersionCheck';
   forceAppRefresh();
   ```

### Development Issues
1. Disable cache in DevTools (Network tab)
2. Use incognito mode for testing
3. Check console for Service Worker logs

### Production Issues
1. Verify build process includes version variables
2. Check server headers for cache control
3. Ensure Service Worker is served from root domain

## Best Practices

### For Deployments
1. Always test in staging environment first
2. Use version bumping for significant changes
3. Monitor user adoption of new versions
4. Keep deployment logs for troubleshooting

### For Development
1. Test version checking in production-like environment
2. Verify Service Worker behavior across browsers
3. Test notification UX with real users
4. Monitor performance impact of version checks

## Security Considerations

### Version Information
- Version numbers are public (visible in client)
- Build hashes help prevent cache poisoning
- No sensitive information in version data

### Service Worker
- Only caches same-origin resources
- Validates response types before caching
- Clears old caches to prevent bloat

## Performance Impact

### Network
- Version checks: ~1KB per request
- Frequency: Every 3 minutes (configurable)
- Background operation (non-blocking)

### Storage
- Service Worker cache: ~5-10MB typical
- localStorage: <1KB for version data
- Automatic cleanup of old caches

### CPU
- Version comparison: Negligible
- Cache operations: Minimal impact
- Background processing only
