import React, { useState, useEffect, useRef, useCallback } from "react";
import { toPng } from 'html-to-image';
import Plot from 'react-plotly.js';
import { Box, TextField, Typography, ClickAwayListener } from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EditIcon from '@mui/icons-material/Edit';
import useAnalytics from '../hooks/useAnalytics';

import InsightsIcon from '@mui/icons-material/Lightbulb';

// Interface matching the expected structure from ChartGrid
interface ChartData {
  id: string | number;
  chart_type: string;
  chart_group?: string;
  library: string;
  allowed_chart_types?: string[] | string; // New field from backend API - can be array or comma-separated string
  fields?: {
    x?: string;
    x_type?: string; // Keep if used
    y?: string;
    y_type?: string; // Keep if used
    numeric?: string;
    agg?: string;
  };
  data: { // This structure might vary greatly
    x?: any[];
    y?: any[];
    z?: any[]; // For heatmap/3D
    type?: string; // e.g., 'bar', 'scatter', 'pie'
    labels?: string[]; // For pie
    values?: number[]; // For pie
    parents?: string[]; // For hierarchy charts (treemap, sunburst, icicle)
    mode?: string; // e.g., 'lines', 'markers'
    name?: string; // Legend entry name
    line?: { width?: number; color?: string; [key: string]: any }; // Line properties
    marker?: { size?: number; color?: string; opacity?: number; line?: { width?: number; color?: string }; [key: string]: any }; // Marker properties
    fill?: string; // Fill properties for area charts
    stackgroup?: string; // Stack group for stacked charts
    diagonal?: { visible?: boolean; [key: string]: any }; // Diagonal properties for splom
    text?: any[]; // Text labels
    textposition?: string; // Text position
    textfont?: { size?: number; color?: string; [key: string]: any }; // Text font
    hovertemplate?: string; // Hover template
    [key: string]: any; // Allow other Plotly properties
    // Allow array of traces for multi-trace charts
  } | Array<{
    x?: any[];
    y?: any[];
    z?: any[];
    type?: string;
    labels?: string[];
    values?: number[];
    parents?: string[]; // For hierarchy charts (treemap, sunburst, icicle)
    mode?: string;
    name?: string;
    line?: { width?: number; color?: string; [key: string]: any }; // Line properties
    marker?: { size?: number; color?: string; opacity?: number; line?: { width?: number; color?: string }; [key: string]: any }; // Marker properties
    fill?: string; // Fill properties for area charts
    stackgroup?: string; // Stack group for stacked charts
    diagonal?: { visible?: boolean; [key: string]: any }; // Diagonal properties for splom
    text?: any[]; // Text labels
    textposition?: string; // Text position
    textfont?: { size?: number; color?: string; [key: string]: any }; // Text font
    hovertemplate?: string; // Hover template
    [key: string]: any; // Allow other Plotly properties
  }>;
  layout: {
    title?: string | { text: string };
    xaxis?: {
      title?: string | { text: string; font?: any; standoff?: number };
      tickfont?: { size?: number; color?: string };
      tickangle?: number;
      gridcolor?: string;
      zerolinecolor?: string;
      automargin?: boolean;
      standoff?: number;
      [key: string]: any;
    }; // Allow other xaxis props
    yaxis?: {
      title?: string | { text: string; font?: any; standoff?: number };
      side?: 'left' | 'right';
      tickfont?: { size?: number; color?: string };
      gridcolor?: string;
      zerolinecolor?: string;
      automargin?: boolean;
      ticklen?: number;
      tickwidth?: number;
      showgrid?: boolean;
      fixedrange?: boolean;
      autorange?: boolean;
      standoff?: number;
      [key: string]: any;
    }; // Allow other yaxis props
    [key: string]: any; // Allow other layout properties like legend, annotations etc.
  };
  available_fields?: {
    categorical?: string[];
    numerical?: string[];
    datetime?: string[];
  };
}

interface ChartProps {
  data: ChartData;
  // availableColumns?: string[]; // Receive available columns if needed for edits
  onChartUpdate?: (updatedFields: any) => Promise<void>; // Expecting a promise indicates async operation
  onInsightsRequest?: (chartId: string | number, chartData: any, position: 'left' | 'center' | 'right') => Promise<any>; // Optional callback for insights
  onInsightsClick?: (chartData: ChartData) => void; // New callback for insights page navigation
  onPreviewClick?: (chartData: ChartData) => void; // New callback for preview modal
  onModifyClick?: (chartData: ChartData) => void; // New callback for chart modification page
  position?: 'left' | 'center' | 'right'; // Position of the chart in the grid
  hideInsightsIcon?: boolean; // Option to hide insights icon (for insights page)
}

interface EditableTextProps {
  value: string;
  onChange: (newValue: string) => void;
  variant?: 'title' | 'axis';
  placeholder?: string;
}

const EditableText: React.FC<EditableTextProps> = ({ value, onChange, variant = 'title', placeholder = 'Click to edit' }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempValue, setTempValue] = useState(value);

  const handleClick = () => {
    setIsEditing(true);
    setTempValue(value);
  };

  const handleClickAway = () => {
    setIsEditing(false);
    if (tempValue !== value) {
      onChange(tempValue);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      setIsEditing(false);
      onChange(tempValue);
    }
  };

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <Box sx={{
        display: 'inline-block',
        width: '100%',
        maxWidth: variant === 'title' ? '100%' : '150px',
        cursor: 'pointer',
        padding: variant === 'axis' ? '3px 6px' : '2px',
        borderRadius: '4px',
        backgroundColor: variant === 'axis' ? 'rgba(255, 255, 255, 0.9)' : 'transparent',
        boxShadow: variant === 'axis' ? '0 1px 3px rgba(0,0,0,0.1)' : 'none',
        '&:hover': {
          backgroundColor: variant === 'axis' ? 'rgba(255, 255, 255, 1)' : 'rgba(0, 0, 0, 0.04)',
          boxShadow: variant === 'axis' ? '0 2px 4px rgba(0,0,0,0.15)' : 'none',
        }
      }}>
        {isEditing ? (
          <TextField
            autoFocus
            value={tempValue}
            onChange={(e) => setTempValue(e.target.value)}
            onKeyDown={handleKeyDown}
            variant="standard"
            fullWidth
            sx={{
              '& .MuiInputBase-input': {
                fontSize: variant === 'title' ? { xs: '0.9rem', sm: '1.1rem', md: '1.2rem' } : { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
                fontWeight: variant === 'title' ? 500 : 500,
                textAlign: 'center',
                padding: '2px 4px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }
            }}
          />
        ) : (
          <Typography
            onClick={handleClick}
            sx={{
              fontSize: variant === 'title' ? { xs: '0.9rem', sm: '1.1rem', md: '1.2rem' } : { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
              fontWeight: variant === 'title' ? 500 : 500,
              textAlign: 'center',
              color: value ? (variant === 'axis' ? '#000000' : 'inherit') : 'text.secondary',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {value || placeholder}
          </Typography>
        )}
      </Box>
    </ClickAwayListener>
  );
};

const Chart: React.FC<ChartProps> = ({ data, onChartUpdate, onInsightsClick, onPreviewClick, onModifyClick, hideInsightsIcon = false }) => {
  const { chart_type, data: chartData, layout = {} } = data;
  const plotContainerRef = useRef<HTMLDivElement>(null); // Ref for container size access
  const [forceRender, setForceRender] = useState(0); // Force re-render state

  // Analytics hook
  const {  trackChartDownload, trackChartEvent } = useAnalytics();

  // Force re-render when chart type or data changes
  useEffect(() => {
    setForceRender(prev => prev + 1);
  }, [chart_type, chartData]);

// Removed CHART_RENDER tracking as requested - these events are not valuable to track

  // Handle insights button click
  const handleInsightsClick = useCallback(() => {
    if (onInsightsClick) {
      onInsightsClick(data);
    }
  }, [onInsightsClick, data]);

  // Handle preview button click
  const handlePreviewClick = useCallback(() => {
    if (onPreviewClick) {
      onPreviewClick(data);
    }
  }, [onPreviewClick, data]);

  // Handle modify button click
  const handleModifyClick = useCallback(() => {
    if (onModifyClick) {
      onModifyClick(data);
    }
  }, [onModifyClick, data]);

  // Function to safely extract text from title objects
  const getTitleText = (title: string | { text: string } | undefined): string => {
    if (typeof title === 'string') {
      return title;
    }
    if (typeof title === 'object' && title !== null && typeof title.text === 'string') {
      return title.text;
    }
    return ''; // Default empty string
  };

  // Initialize state directly from the potentially complex layout object
  const [chartTitle, setChartTitle] = useState(getTitleText(layout.title));
  const [xAxisLabel, setXAxisLabel] = useState(getTitleText(layout.xaxis?.title));
  const [yAxisLabel, setYAxisLabel] = useState(getTitleText(layout.yaxis?.title));

  const handleTitleChange = useCallback((newTitle: string) => {
    setChartTitle(newTitle);
    if (onChartUpdate) {
      const updatedFields = {
        chart_title: newTitle,
        x_axis_title: xAxisLabel,
        y_axis_title: yAxisLabel
      };
      onChartUpdate(updatedFields);
    }
  }, [onChartUpdate, xAxisLabel, yAxisLabel]);

  const handleXAxisChange = useCallback((newLabel: string) => {
    setXAxisLabel(newLabel);
    if (onChartUpdate) {
      const updatedFields = {
        chart_title: chartTitle,
        x_axis_title: newLabel,
        y_axis_title: yAxisLabel
      };
      onChartUpdate(updatedFields);
    }
  }, [onChartUpdate, chartTitle, yAxisLabel]);

  const handleYAxisChange = useCallback((newLabel: string) => {
    setYAxisLabel(newLabel);
    if (onChartUpdate) {
      const updatedFields = {
        chart_title: chartTitle,
        x_axis_title: xAxisLabel,
        y_axis_title: newLabel
      };
      onChartUpdate(updatedFields);
    }
  }, [onChartUpdate, chartTitle, xAxisLabel]);

  // Update state if the incoming data prop changes (e.g., after an update)
  useEffect(() => {
    setChartTitle(getTitleText(layout.title));
    setXAxisLabel(getTitleText(layout.xaxis?.title));
    setYAxisLabel(getTitleText(layout.yaxis?.title));
  }, [data, layout]); // Dependency array includes the whole data object


  // --- End Get Available Fields ---

  // Uncomment this code block when insights feature is ready
  /*
  const handleInsightsRequest = useCallback(async () => {
    try {
      setIsLoadingInsights(true);

      // Prepare aggregate data from the chart
      const aggregateData = {
        chart_id: data.id,
        chart_type: chart_type,
        chart_data: chartData,
        // Add any additional aggregated data that might be useful
        summary: {
          title: chartTitle,
          x_axis: xAxisLabel,
          y_axis: yAxisLabel
        }
      };

      // If callback is provided, use it (this will use the ChartGrid's implementation)
      if (onInsightsRequest) {
        const insights = await onInsightsRequest(data.id, aggregateData, position);
        setIsLoadingInsights(false);
        return insights;
      }

      // Otherwise make direct API call using our service
      const insights = await getChartInsights(data.id, aggregateData);
      console.log('Chart insights:', insights);

      // Show success message
      setSnackbarMessage('Insights generated successfully');
      setSnackbarOpen(true);

      setIsLoadingInsights(false);
      return insights;

    } catch (error) {
      console.error('Error getting chart insights:', error);
      setIsLoadingInsights(false);

      // Show error message
      setSnackbarMessage('Failed to generate insights');
      setSnackbarOpen(true);

      return null;
    }
  }, [data.id, chart_type, chartData, chartTitle, xAxisLabel, yAxisLabel, position, onInsightsRequest]);
  */

  // Function to copy chart to clipboard
  const handleCopyChart = useCallback(async (event: React.MouseEvent<HTMLElement>) => {
    if (!plotContainerRef.current) {
      console.warn('Chart not ready for copying');
      return;
    }

    // Get the specific button that was clicked
    const copyButton = event.currentTarget;
    let originalContent = '';
    let originalTitle = '';

    if (copyButton) {
      originalContent = copyButton.innerHTML;
      originalTitle = copyButton.getAttribute('title') || '';
      copyButton.innerHTML = '⏳';
      copyButton.setAttribute('title', 'Copying...');
    }

    try {
      // Hide action buttons temporarily
      const actionButtons = plotContainerRef.current.querySelector('.action-buttons');
      let originalDisplay = 'flex';
      if (actionButtons instanceof HTMLElement) {
        originalDisplay = actionButtons.style.display || 'flex';
        actionButtons.style.display = 'none';
      }

      // Create PNG
      const dataUrl = await toPng(plotContainerRef.current, {
        backgroundColor: 'transparent',
        quality: 1.0,
        pixelRatio: 2,
      });

      // Restore action buttons display
      if (actionButtons instanceof HTMLElement) {
        actionButtons.style.display = originalDisplay;
      }

      // Create a temporary image element
      const img = document.createElement('img');
      img.src = dataUrl;

      // When the image loads, copy it to clipboard
      img.onload = async () => {
        try {
          // Create a canvas element
          const canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;

          // Draw the image on the canvas
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.drawImage(img, 0, 0);

            // Convert canvas to blob and copy to clipboard
            canvas.toBlob(async (blob) => {
              if (blob) {
                try {
                  // Use the Clipboard API to copy the image
                  const item = new ClipboardItem({ 'image/png': blob });
                  await navigator.clipboard.write([item]);

                  // Track successful copy
                  await trackChartEvent('CHART_COPY', chart_type, data.id, {
                    success: true,
                    format: 'png',
                    chart_title: getTitleText(layout.title)
                  });

                  // Show success feedback
                  if (copyButton) {
                    copyButton.innerHTML = '✅';
                    copyButton.setAttribute('title', 'Copied successfully!');
                    setTimeout(() => {
                      copyButton.innerHTML = originalContent;
                      copyButton.setAttribute('title', originalTitle);
                    }, 2000);
                  }

                  console.log('✅ Chart copied to clipboard successfully!');
                } catch (clipboardError) {
                  console.error('Error copying to clipboard:', clipboardError);

                  // Track copy failure
                  await trackChartEvent('ERROR_OCCURRED', chart_type, data.id, {
                    error_type: 'clipboard_copy_failed',
                    error_message: clipboardError instanceof Error ? clipboardError.message : 'Unknown error'
                  });

                  // Show error feedback
                  if (copyButton) {
                    copyButton.innerHTML = '❌';
                    copyButton.setAttribute('title', 'Copy failed - try again');
                    setTimeout(() => {
                      copyButton.innerHTML = originalContent;
                      copyButton.setAttribute('title', originalTitle);
                    }, 2000);
                  }
                }
              } else {
                // Show error feedback
                if (copyButton) {
                  copyButton.innerHTML = '❌';
                  copyButton.setAttribute('title', 'Failed to create image');
                  setTimeout(() => {
                    copyButton.innerHTML = originalContent;
                    copyButton.setAttribute('title', originalTitle);
                  }, 2000);
                }
              }
            }, 'image/png');
          } else {
            // Show error feedback
            if (copyButton) {
              copyButton.innerHTML = '❌';
              copyButton.setAttribute('title', 'Failed to create canvas');
              setTimeout(() => {
                copyButton.innerHTML = originalContent;
                copyButton.setAttribute('title', originalTitle);
              }, 2000);
            }
          }
        } catch (canvasError) {
          console.error('Error creating canvas:', canvasError);

          // Track canvas error
          await trackChartEvent('ERROR_OCCURRED', chart_type, data.id, {
            error_type: 'canvas_creation_failed',
            error_message: canvasError instanceof Error ? canvasError.message : 'Unknown error'
          });

          // Show error feedback
          if (copyButton) {
            copyButton.innerHTML = '❌';
            copyButton.setAttribute('title', 'Processing failed');
            setTimeout(() => {
              copyButton.innerHTML = originalContent;
              copyButton.setAttribute('title', originalTitle);
            }, 2000);
          }
        }
      };

      // Handle image load error
      img.onerror = () => {
        if (copyButton) {
          copyButton.innerHTML = '❌';
          copyButton.setAttribute('title', 'Image load failed');
          setTimeout(() => {
            copyButton.innerHTML = originalContent;
            copyButton.setAttribute('title', originalTitle);
          }, 2000);
        }
      };
    } catch (error) {
      console.error('Error copying chart to clipboard:', error);

      // Track general copy error
      await trackChartEvent('ERROR_OCCURRED', chart_type, data.id, {
        error_type: 'chart_copy_failed',
        error_message: error instanceof Error ? error.message : 'Unknown error'
      });

      // Show error feedback
      if (copyButton) {
        copyButton.innerHTML = '❌';
        copyButton.setAttribute('title', 'Copy failed');
        setTimeout(() => {
          copyButton.innerHTML = originalContent;
          copyButton.setAttribute('title', originalTitle);
        }, 2000);
      }
    }
  }, [chart_type, data.id, data.chart_group, layout.title, trackChartEvent]);

  // Function to export chart as PNG
  const handleSaveChart = useCallback(async () => {
    if (!plotContainerRef.current) return;

    try {
      // Get the chart title for the filename
      const chartTitle = getTitleText(data.layout.title) || 'chart';
      const filename = `${chartTitle.toLowerCase().replace(/\s+/g, '_')}.png`;


      // Hide action buttons temporarily
      const actionButtons = plotContainerRef.current.querySelector('.action-buttons');
      let originalDisplay = 'flex';
      if (actionButtons instanceof HTMLElement) {
        originalDisplay = actionButtons.style.display || 'flex';
        actionButtons.style.display = 'none';
      }

      // Create PNG
      const dataUrl = await toPng(plotContainerRef.current, {
        backgroundColor: 'transparent',
        quality: 1.0,
        pixelRatio: 2,
      });

      // Restore action buttons display
      if (actionButtons instanceof HTMLElement) {
        actionButtons.style.display = originalDisplay;
      }

      // Create download link
      const link = document.createElement('a');
      link.download = filename;
      link.href = dataUrl;
      link.click();

      // Track successful download
      await trackChartEvent('CHART_DOWNLOAD', chart_type, data.id, {
        success: true,
        format: 'png',
        filename: filename,
        chart_title: chartTitle
      });

      // Show success message in console and via button feedback
      console.log('✅ Chart downloaded successfully!');

    } catch (error) {
      console.error('❌ Error saving chart as PNG:', error);

      // Track download error
      await trackChartEvent('ERROR_OCCURRED', chart_type, data.id, {
        error_type: 'chart_download_failed',
        error_message: error instanceof Error ? error.message : 'Unknown error',
        format: 'png'
      });
    }
  }, [data.layout.title, chart_type, data.id, data.chart_group, trackChartDownload, trackChartEvent]);

  // --- Prepare Data Logic ---
   const prepareData = useCallback(() => {
       let plotData = Array.isArray(chartData) ? chartData : [chartData]; // Ensure it's an array of traces

       // Handle specific chart type transformations
       const isDonutChart = ['donut', 'doughnut'].includes(chart_type.toLowerCase());
       if (chart_type === 'pie' || isDonutChart) {
            // Plotly pie needs 'labels' and 'values'
            return plotData.map(trace => ({
                ...trace, // Keep other properties like name, hoverinfo etc.
                type: 'pie',
                labels: trace.labels || trace.x, // Use labels if present, otherwise fallback to x
                values: trace.values || trace.y, // Use values if present, otherwise fallback to y
                // Ensure x and y are removed if they were used as source, to avoid conflicts
                x: undefined,
                y: undefined,
                hole: isDonutChart ? 0.4 : 0, // Make donuts distinct with a hole
                automargin: true, // Helps fit labels
                textinfo: 'percent+value', // Show only percentages and values, not labels (categories shown in legend)
                textposition: 'auto',
                textfont: {
                    size: 11,
                    color: '#212121',
                    family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
                },
                hovertemplate: '<b>%{label}</b><br>Value: %{value}<br>Percent: %{percent}<extra></extra>',
            }));
        }

       // Handle semi circle charts (half pie chart)
       if (chart_type === 'semi_circle') {
            // First, let's try to render a basic pie chart to ensure it works
            return plotData.map(trace => {
                // Get original values and labels
                const values = trace.values || trace.y || [30, 25, 20, 15, 10];
                const labels = trace.labels || trace.x || ['Category A', 'Category B', 'Category C', 'Category D', 'Category E'];

                console.log('Creating basic pie for semi-circle, values:', values, 'labels:', labels);

                return {
                    type: 'pie',
                    labels: labels,
                    values: values,
                    textinfo: 'percent+value',
                    textposition: 'auto',
                    textfont: {
                        size: 12,
                        color: '#212121'
                    },
                    marker: {
                        colors: trace.marker?.colors || ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'],
                        line: {
                            color: '#FFFFFF',
                            width: 2
                        }
                    },
                    showlegend: true
                };
            });
       }

       if (chart_type === 'heatmap') {
           return plotData.map(trace => {
               // Helper function to determine if a color is light or dark
               const isLightColor = (colorscale: any) => {
                   // For heatmap, we need to determine the overall brightness of the colorscale
                   // Since heatmap cells will have varying colors, we'll use a smart approach
                   // that works well with most colorscales by checking the middle color

                   if (!colorscale || typeof colorscale === 'string') {
                       // For string colorscales like 'Viridis', 'Plasma', etc., we know they're generally dark
                       const lightColorscales = ['Blues', 'Reds', 'Greens', 'Oranges', 'Purples'];
                       const colorscaleName = colorscale || 'Viridis';
                       return lightColorscales.some(light => colorscaleName.includes(light));
                   }

                   if (Array.isArray(colorscale) && colorscale.length > 0) {
                       // For custom colorscales, check the middle color
                       const middleIndex = Math.floor(colorscale.length / 2);
                       const middleColor = colorscale[middleIndex];
                       const color = Array.isArray(middleColor) ? middleColor[1] : middleColor;

                       // Convert hex to RGB and calculate luminance
                       const hex = color.replace('#', '');
                       const r = parseInt(hex.substr(0, 2), 16);
                       const g = parseInt(hex.substr(2, 2), 16);
                       const b = parseInt(hex.substr(4, 2), 16);

                       // Calculate relative luminance using WCAG formula
                       const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
                       return luminance > 0.5; // Light if luminance > 0.5
                   }

                   return false; // Default to dark text on light background
               };

               const colorscale = trace.colorscale || 'Viridis';
               const isLight = isLightColor(colorscale);

               return {
                   ...trace,
                   type: 'heatmap',
                   // Ensure z, x, y are correctly assigned if not already
                   z: trace.z || trace.values, // Assuming z or values holds the heatmap data
                   x: trace.x || trace.labels, // Columns
                   y: trace.y, // Rows
                   // Use colorscale from trace if available (set by ChartModifyPage), otherwise default to Viridis
                   colorscale: colorscale,
                   // Preserve other colorscale-related properties from trace
                   showscale: trace.showscale !== undefined ? trace.showscale : true,
                   colorbar: trace.colorbar || {
                       thickness: 15,
                       len: 0.7
                   },
                   // Display numeric values inside heatmap cells
                   text: trace.z || trace.values, // Use the same data for text display
                   texttemplate: '%{text}', // Show the actual values
                   textfont: {
                       size: 12,
                       color: isLight ? '#000000' : '#FFFFFF' // Black text on light colors, white text on dark colors
                   },
                   hovertemplate: '<b>%{x}</b><br><b>%{y}</b><br>Value: %{z}<extra></extra>',
               };
           });
       }

       if (chart_type === 'boxplot') {
           return plotData.map(trace => ({
               ...trace,
               type: 'box',
               // y usually contains the numerical data for box plots
               y: trace.y || trace.values,
               x: trace.x || trace.labels // x can be used for grouping categories
           }));
       }

       // Handle sunburst chart
       if (chart_type.toLowerCase() === 'sunburst') {
           return plotData.map(trace => {
               // No need to calculate total value for the center display anymore

               return {
                   ...trace,
                   type: 'sunburst',
                   // Sunburst charts typically use labels, parents, values
                   labels: trace.labels || trace.x,
                   parents: trace.parents || [],
                   values: trace.values || trace.y,
                   // Remove x and y to avoid conflicts
                   x: undefined,
                   y: undefined,
                   branchvalues: 'total',
                   // Add white lines between sectors for better visual separation, preserve existing marker properties
                   marker: {
                       ...trace.marker, // Preserve existing marker properties (including colors from ChartModifyPage)
                       line: { width: 3, color: 'white' } // Increased width for better visibility
                   },
                   // Improve text contrast and visibility
                   insidetextfont: { color: '#ffffff', size: 12 },
                   outsidetextfont: { color: '#000000', size: 12 },
                   // Create a hole in the center for displaying the total
                   hole: 0.5, // Increased hole size for better visibility of the total
                   // Configure text display for sectors
                   textinfo: 'label',
                   hoverinfo: 'label+value+percent parent',
                   // Don't show text in the center (we'll use annotations instead)
                   insidetextorientation: 'radial'
               };
           });
       }

       // Handle icicle chart
       if (chart_type.toLowerCase() === 'icicle') {
           return plotData.map(trace => {
               // No need to calculate total here as we're using the global total

               return {
                   ...trace,
                   type: 'icicle',
                   // Icicle charts typically use labels, parents, values
                   labels: trace.labels || trace.x,
                   parents: trace.parents || [],
                   values: trace.values || trace.y,
                   // Remove x and y to avoid conflicts
                   x: undefined,
                   y: undefined,
                   branchvalues: 'total',
                   // Add white lines between sectors for better visual separation, preserve existing marker properties
                   marker: {
                       ...trace.marker, // Preserve existing marker properties (including colors from ChartModifyPage)
                       line: { width: 2, color: 'white' }
                   },
                   // Improve text contrast and visibility
                   insidetextfont: { color: '#ffffff', size: 12 },
                   outsidetextfont: { color: '#000000', size: 12 },
                   // Configure text display for sectors
                   textinfo: 'label',
                   hoverinfo: 'label+value+percent parent',
                   // Root label settings
                   root: {
                       color: 'rgba(255,255,255,0)',
                       label: '', // Remove 'Total' text from root to avoid duplication
                       text: ''   // Remove formatted total from root to avoid duplication
                   },
                   pathbar: {
                       visible: false
                   }
               };
           });
       }

       // Handle treemap chart
       if (chart_type.toLowerCase() === 'treemap') {
           return plotData.map(trace => {
               return {
                   ...trace,
                   type: 'treemap',
                   // Treemap charts typically use labels, parents, values
                   labels: trace.labels || trace.x,
                   parents: trace.parents || [],
                   values: trace.values || trace.y,
                   // Remove x and y to avoid conflicts
                   x: undefined,
                   y: undefined,
                   branchvalues: 'total',
                   // Add white lines between sectors for better visual separation, preserve existing marker properties
                   marker: {
                       ...trace.marker, // Preserve existing marker properties (including colors from ChartModifyPage)
                       line: { width: 1, color: 'white' }
                   },
                   // Configure text display for sectors
                   textinfo: 'label',
                   hoverinfo: 'label+value+percent parent',
                   // Root label settings
                   root: {
                       color: 'rgba(255,255,255,0)',
                       label: ''
                   }
               };
           });
       }

       // Handle bar charts with numeric value display
       if (chart_type === 'bar') {
           return plotData.map(trace => ({
               ...trace,
               type: 'bar',
               // Display numeric values on top of bars
               text: trace.y || trace.values, // Use y values for text display
               textposition: 'outside', // Position text outside the bars
               textfont: {
                   size: 11,
                   color: '#212121', // Dark text for better readability
                   family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
               },
               hovertemplate: '<b>%{x}</b><br>Value: %{y}<extra></extra>',
               cliponaxis: false, // Prevent text from being clipped
           }));
       }

       // Handle horizontal bar charts
       if (chart_type === 'horizontal_bar') {
           return plotData.map(trace => {
               // For horizontal bars, we need to swap x and y data
               const originalX = trace.x || trace.labels || [];
               const originalY = trace.y || trace.values || [];

               return {
                   ...trace,
                   type: 'bar',
                   orientation: 'h', // Horizontal orientation
                   // Swap x and y for horizontal display
                   x: originalY, // Values go on x-axis (horizontal)
                   y: originalX, // Categories go on y-axis (vertical)
                   // Display numeric values at the end of bars
                   text: originalY, // Use the values for text display
                   textposition: 'outside', // Position text outside the bars
                   textfont: {
                       size: 11,
                       color: '#212121', // Dark text for better readability
                       family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
                   },
                   // Update hover template for horizontal orientation
                   hovertemplate: '<b>%{y}</b><br>Value: %{x}<extra></extra>',
                   cliponaxis: false, // Prevent text from being clipped
               };
           });
       }

       // Handle stacked bar charts - must come before general bar chart handler
       if (chart_type.toLowerCase().includes('stacked') && chart_type.toLowerCase().includes('bar')) {
           const isHorizontal = chart_type.toLowerCase().includes('horizontal');

           return plotData.map((trace, index) => {
               // For horizontal stacked bars, we need to swap x and y data
               let xData = trace.x || trace.labels || [];
               let yData = trace.y || trace.values || [];
               let textData = yData;
               let hoverTemplate = '<b>%{fullData.name}</b><br>%{x}<br>Value: %{y}<extra></extra>';

               if (isHorizontal) {
                   // Swap x and y for horizontal display
                   const originalX = xData;
                   const originalY = yData;
                   xData = originalY; // Values go on x-axis (horizontal)
                   yData = originalX; // Categories go on y-axis (vertical)
                   textData = originalY; // Use the values for text display
                   hoverTemplate = '<b>%{fullData.name}</b><br>%{y}<br>Value: %{x}<extra></extra>';
               }

               return {
                   ...trace,
                   type: 'bar',
                   mode: undefined,
                   orientation: isHorizontal ? 'h' : undefined,
                   x: xData,
                   y: yData,
                   // Ensure each trace has a name for the legend
                   name: trace.name || `Series ${index + 1}`,
                   // Display numeric values on bars
                   text: textData,
                   textposition: 'inside', // Inside for stacked bars
                   textfont: {
                       size: 10,
                       color: '#FFFFFF', // White text for better contrast on colored bars
                       family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
                   },
                   // Configure stacking for bar charts - bars with same x values will stack automatically
                   hovertemplate: hoverTemplate,
                   cliponaxis: false
               };
           });
       }

       // Handle stacked line charts - must come before general line chart handler
       if (chart_type.toLowerCase().includes('stacked') && chart_type.toLowerCase().includes('line')) {
           return plotData.map((trace) => ({
               ...trace,
               type: 'scatter',
               mode: trace.mode || 'lines+markers', // Lines with markers for stacked line
               line: {
                   width: 2,
                   color: trace.line?.color || trace.marker?.color, // Use consistent color
                   ...trace.line
               },
               marker: {
                   size: 4,
                   color: trace.marker?.color || trace.line?.color, // Use consistent color
                   ...trace.marker
               },
               fill: undefined, // No fill for stacked line charts - they should be lines, not areas
               // Configure stacking for line charts
               stackgroup: trace.stackgroup || 'one', // Group traces for stacking
               // Enhanced hover template
               hovertemplate: '<b>%{fullData.name}</b><br>%{x}<br>Value: %{y}<extra></extra>',
           }));
       }

       // Handle multi-line charts
       if (chart_type.toLowerCase().includes('multi_line')) {
           return plotData.map((trace) => ({
               ...trace,
               type: 'scatter',
               mode: trace.mode || 'lines+markers', // Lines with markers for multi-line
               line: {
                   width: 2.5,
                   color: trace.line?.color || trace.marker?.color, // Use consistent color
                   ...trace.line
               },
               marker: {
                   size: 5,
                   color: trace.marker?.color || trace.line?.color, // Use consistent color
                   ...trace.marker
               },
               fill: undefined, // Remove any fill for line charts
               stackgroup: undefined, // Remove stacking for line charts
               // Enhanced hover template for multi-line charts
               hovertemplate: '<b>%{fullData.name}</b><br>%{x}<br>Value: %{y}<extra></extra>',
           }));
       }

       // Handle multi-area charts
       if (chart_type.toLowerCase().includes('multi_area')) {
           return plotData.map((trace, index) => ({
               ...trace,
               type: 'scatter',
               mode: 'lines',
               fill: trace.fill || (index === 0 ? 'tozeroy' : 'tonexty'), // First trace fills to zero, others stack
               line: {
                   width: 0, // No line for area charts
                   color: trace.line?.color || trace.marker?.color, // Use consistent color
                   ...trace.line
               },
               marker: {
                   color: trace.marker?.color || trace.line?.color, // Use consistent color
                   ...trace.marker
               },
               // Ensure proper stacking for area charts
               stackgroup: trace.stackgroup || 'one', // Group traces for stacking
               // Enhanced hover template
               hovertemplate: '<b>%{fullData.name}</b><br>%{x}<br>Value: %{y}<extra></extra>',
           }));
       }

       // Handle combo bar line charts
       if (chart_type.toLowerCase().includes('combo_bar_line')) {
           return plotData.map((trace, index) => {
               // Ensure we have mixed types - alternate between bar and line
               const isBarTrace = trace.type === 'bar' || index % 2 === 0;

               if (isBarTrace) {
                   return {
                       ...trace,
                       type: 'bar',
                       mode: undefined,
                       orientation: undefined,
                       // Display numeric values on top of bars
                       text: trace.y || trace.values,
                       textposition: 'outside',
                       textfont: {
                           size: 11,
                           color: '#212121',
                           family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
                       },
                       marker: {
                           ...trace.marker,
                           // Ensure bar color is applied
                       },
                       hovertemplate: '<b>%{x}</b><br>Value: %{y}<extra></extra>',
                       cliponaxis: false
                   };
               } else {
                   return {
                       ...trace,
                       type: 'scatter',
                       mode: trace.mode || 'lines+markers',
                       line: {
                           width: 2.5,
                           color: trace.line?.color || trace.marker?.color, // Use consistent color
                           ...trace.line
                       },
                       marker: {
                           size: 5,
                           color: trace.marker?.color || trace.line?.color, // Use consistent color
                           ...trace.marker
                       },
                       hovertemplate: '<b>%{x}</b><br>Value: %{y}<extra></extra>',
                   };
               }
           });
       }

       // Handle multi-metric charts (multiple lines/series) - fallback for other multi charts
       if (chart_type.toLowerCase().includes('multi') || chart_type.toLowerCase().includes('multiple')) {
           return plotData.map((trace) => ({
               ...trace,
               type: 'scatter',
               mode: trace.mode || 'lines+markers', // Lines with markers for multi-metric
               line: {
                   width: 2.5,
                   ...trace.line
               },
               marker: {
                   size: 5,
                   ...trace.marker
               },
               // Enhanced hover template for multi-metric charts
               hovertemplate: '<b>%{fullData.name}</b><br>%{x}<br>Value: %{y}<extra></extra>',
           }));
       }

       // Handle regular line charts with numeric value display (including column mapped to line)
       if (chart_type === 'line' || chart_type === 'column' || chart_type === 'scatter') {
           return plotData.map(trace => ({
               ...trace,
               type: 'scatter',
               mode: trace.mode || 'lines+markers+text', // Include text mode
               // Display numeric values next to data points
               text: trace.y || trace.values, // Use y values for text display
               textposition: 'top center', // Position text above the points
               textfont: {
                   size: 10,
                   color: '#212121', // Dark text for better readability
                   family: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif'
               },
               hovertemplate: '<b>%{x}</b><br>Value: %{y}<extra></extra>',
               cliponaxis: false, // Prevent text from being clipped
           }));
       }

       // Handle scatter matrix (splom) charts
       if (['splom', 'scattermatrix', 'scatter_matrix'].includes(chart_type.toLowerCase())) {
           return plotData.map(trace => ({
               ...trace,
               type: 'splom', // Plotly scatter plot matrix type
               // Ensure proper marker configuration for visibility
               marker: {
                   ...trace.marker,
                   size: trace.marker?.size || 4, // Smaller markers for matrix view
                   opacity: trace.marker?.opacity || 0.7, // Semi-transparent for overlapping points
                   line: {
                       width: 0.5,
                       color: 'rgba(0,0,0,0.2)'
                   }
               },
               // Configure diagonal plots if needed
               diagonal: {
                   visible: trace.diagonal?.visible !== false, // Show diagonal by default
                   ...trace.diagonal
               },
               // Simplified hover template for matrix view
               hovertemplate: '%{x}<br>%{y}<extra></extra>',
           }));
       }

       // Handle area charts (including stacked area)
       if (chart_type.toLowerCase().includes('area')) {
           return plotData.map((trace, index) => ({
               ...trace,
               type: 'scatter',
               mode: 'lines',
               fill: trace.fill || (index === 0 ? 'tozeroy' : 'tonexty'), // First trace fills to zero, others stack
               line: {
                   width: 0, // No line for area charts
                   ...trace.line
               },
               // Ensure proper stacking for area charts
               stackgroup: trace.stackgroup || 'one', // Group traces for stacking
               // Enhanced hover template
               hovertemplate: '<b>%{fullData.name}</b><br>%{x}<br>Value: %{y}<extra></extra>',
           }));
       }

       // General case: pass through, ensure type is set if missing based on chart_type
       return plotData.map(trace => ({
           ...trace,
           type: trace.type || chart_type, // Use trace type if specified, else fallback to main chart_type
       }));

   }, [chartData, chart_type]);
   // --- End Prepare Data ---

  // --- Get Layout Logic ---
  const getLayout = useCallback(() => {
    // Start with the layout provided by the API/props
    const baseLayout = {
      ...layout, // Spread the original layout first
      title: { // Hide the Plotly title since we're using our own editable title
        text: '', // Empty text to hide the title
        font: { size: 16, color: '#212121', family: 'Arial, sans-serif' },
        x: 0.5, xanchor: 'center', y: 0.95, yanchor: 'top'
      },
      xaxis: {
        ...(layout.xaxis || {}), // Spread original xaxis settings
        title: { // Hide the Plotly x-axis title since we're using our own editable title
          text: '', // Empty text to hide the title
          font: { size: 12, color: '#212121', family: 'Arial, sans-serif' },
          standoff: 15 // Space between label and axis
        },
        tickfont: { size: 9, color: '#424242' },
        gridcolor: 'rgba(0, 0, 0, 0.1)',
        zerolinecolor: 'rgba(0, 0, 0, 0.2)',
        automargin: true, // Let Plotly adjust margin for labels
      },
      yaxis: {
        ...(layout.yaxis || {}), // Spread original yaxis settings
        title: { // Hide the Plotly y-axis title since we're using our own editable title
          text: '', // Empty text to hide the title
          font: { size: 12, color: '#212121', family: 'Arial, sans-serif' },
          standoff: 15
        },
        tickfont: { size: 9, color: '#424242' },
        gridcolor: 'rgba(0, 0, 0, 0.1)',
        zerolinecolor: 'rgba(0, 0, 0, 0.2)',
        automargin: true,
        ticklen: 4,
        tickwidth: 1,
        showgrid: true,
        fixedrange: false,
        autorange: true,
        side: 'left',
      },
      // Default settings for background, font, margins, legend
      paper_bgcolor: 'transparent', // Make background transparent
      plot_bgcolor: 'transparent', // Make plot area transparent
      font: {
        family: 'Arial, sans-serif',
        color: '#212121', // Default text color
        size: 12
      },
      margin: { // Minimal margins since axis titles are handled outside the plot
        l: 35, r: 10, b: 25, t: 10, pad: 2
      },
      legend: {
          ...(layout.legend || {}), // Keep original legend settings if any
          font: { size: 10, color: '#212121' },
          bgcolor: 'rgba(255,255,255,0.9)', // Slightly transparent background for legend
          bordercolor: 'rgba(0, 0, 0, 0.1)'
      },
      autosize: true, // IMPORTANT: Allow Plotly to resize with container
      // Remove fixed width/height from layout - let CSS handle it
      width: undefined,
      height: undefined,
    };

     // Specific adjustments - declare all chart type variables first
     const isHierarchyChart = ['treemap', 'sunburst', 'icicle'].includes(chart_type.toLowerCase());
     const isDonutChart = ['donut', 'doughnut'].includes(chart_type.toLowerCase());
     const isPieOrDonut = chart_type === 'pie' || isDonutChart;
     const isSemiCircle = chart_type === 'semi_circle';
     const isScatterMatrix = ['splom', 'scattermatrix', 'scatter_matrix'].includes(chart_type.toLowerCase());
     const isStackedArea = chart_type.toLowerCase().includes('area') || (Array.isArray(chartData) && chartData.some(trace => trace.fill === 'tonexty' || trace.fill === 'tozeroy'));
     const isStackedLine = chart_type.toLowerCase().includes('stacked') && chart_type.toLowerCase().includes('line') || (Array.isArray(chartData) && chartData.length > 1 && chartData.some(trace => trace.stackgroup));
     const isStackedBar = chart_type.toLowerCase().includes('stacked') && chart_type.toLowerCase().includes('bar');
     const isMultiChart = (chart_type.toLowerCase().includes('multi') || chart_type.toLowerCase().includes('multiple') || chart_type.toLowerCase().includes('combo')) && (Array.isArray(chartData) && chartData.length > 1);
     const isStackedChart = isStackedArea || isStackedLine || isStackedBar;
     const isChartWithLegend = isStackedChart || isMultiChart;
     const isHorizontalBar = chart_type === 'horizontal_bar' || chart_type === 'horizontal_stacked_bar';

    // Set barmode for stacked bar charts
    if (isStackedBar) {
      (baseLayout as any).barmode = 'stack';
    }

     if (isPieOrDonut || isHierarchyChart || isSemiCircle) {
         // Special handling for semi-circle charts to maximize chart size
         if (isSemiCircle) {
             baseLayout.margin = { l: 30, r: 30, b: 20, t: 50, pad: 5 }; // Better margins for semi-circle display

             // First, let's get the basic pie chart working, then add clipping
             // Temporarily disable shape clipping to debug the basic rendering
             console.log('Semi-circle layout applied - basic pie chart should show');
         } else if (chart_type.toLowerCase() === 'sunburst') {
             // Larger margins for sunburst charts to make them bigger and more visible
             baseLayout.margin = { l: 20, r: 20, b: 20, t: 50, pad: 2 }; // Reduced margins for larger sunburst display
         } else {
             baseLayout.margin = { l: 30, r: 30, b: 30, t: 60, pad: 4 }; // Standard margins for pie/donut and other hierarchy charts
         }

         (baseLayout as any).showlegend = true; // Usually want legend for these chart types
         baseLayout.legend = { ...baseLayout.legend, orientation: 'v', x: 1.05, y: 0.5 }; // Place legend vertically to the right

         // Hide axis completely for pie/donut/semi-circle and hierarchy charts
         (baseLayout as any).xaxis = {
             ...(baseLayout as any).xaxis,
             showticklabels: false,
             showgrid: false,
             zeroline: false
         };

         (baseLayout as any).yaxis = {
             ...(baseLayout as any).yaxis,
             showticklabels: false,
             showgrid: false,
             zeroline: false
         };
     }

     // Calculate total for all traces - used by all hierarchy charts
     let totalSum = 0;
     if (Array.isArray(chartData)) {
         chartData.forEach(trace => {
             const values = trace.values || trace.y || [];
             if (Array.isArray(values)) {
                 totalSum += values.reduce((sum, val) => sum + (Number(val) || 0), 0);
             }
         });
     } else if (chartData) {
         const values = chartData.values || chartData.y || [];
         if (Array.isArray(values)) {
             totalSum += values.reduce((sum, val) => sum + (Number(val) || 0), 0);
         }
     }

     // Format total for use in annotations if needed
     // const formattedTotal = totalSum.toLocaleString(); // Commented out as it's not used anymore

     // Additional specific settings for sunburst charts
     if (chart_type.toLowerCase() === 'sunburst') {
         // Only set default colorway if no custom colors are applied via marker.colors
         const hasCustomColors = Array.isArray(chartData) ?
             chartData.some(trace => trace.marker?.colors) :
             chartData?.marker?.colors;

         if (!hasCustomColors) {
             // Enhance sunburst chart appearance with default colors only if no custom colors
             (baseLayout as any).sunburstcolorway = [
                 '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                 '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
             ]; // Custom color palette
         }

         // Remove Total text and count from center
         (baseLayout as any).annotations = [];
     }

     // Additional specific settings for treemap charts
     if (chart_type.toLowerCase() === 'treemap') {
         // Only set default colorway if no custom colors are applied via marker.colors
         const hasCustomColors = Array.isArray(chartData) ?
             chartData.some(trace => trace.marker?.colors) :
             chartData?.marker?.colors;

         if (!hasCustomColors) {
             // Enhance treemap chart appearance with default colors only if no custom colors
             (baseLayout as any).treemapcolorway = [
                 '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                 '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
             ]; // Custom color palette
         }

         // Add margin to ensure the total label is visible
         baseLayout.margin = {
             ...baseLayout.margin,
             t: 50, // Top margin
             l: 10, // Left margin
             r: 10, // Right margin
             b: 10  // Bottom margin
         };

         // Remove Total text and count
         (baseLayout as any).annotations = [];
     }

     // Special handling for scatter matrix charts to prevent label overlap
     if (isScatterMatrix) {
         // Increase margins significantly for scatter matrix charts
         baseLayout.margin = {
             l: 150, // Increased left margin for y-axis labels
             r: 80,  // Increased right margin for additional space
             b: 120, // Increased bottom margin for x-axis labels
             t: 80,  // Increased top margin
             pad: 8  // Increased padding
         };

         // Configure x-axis for better label spacing
         baseLayout.xaxis = {
             ...baseLayout.xaxis,
             tickfont: { size: 8, color: '#424242' }, // Smaller font size
             tickangle: -45, // Rotate labels to prevent overlap
             automargin: true,
             standoff: 20, // More space between labels and axis
         };

         // Configure y-axis for better label spacing
         baseLayout.yaxis = {
             ...baseLayout.yaxis,
             tickfont: { size: 8, color: '#424242' }, // Smaller font size
             automargin: true,
             standoff: 20, // More space between labels and axis
         };

         // Ensure proper spacing for multi-axis scatter matrix
         if (layout.xaxis2 || layout.yaxis2) {
             // Handle multiple axes if present
             Object.keys(layout).forEach(key => {
                 if (key.startsWith('xaxis') || key.startsWith('yaxis')) {
                     (baseLayout as any)[key] = {
                         ...(layout as any)[key],
                         tickfont: { size: 8, color: '#424242' },
                         automargin: true,
                         standoff: 15,
                         ...(key.startsWith('xaxis') && { tickangle: -45 })
                     };
                 }
             });
         }
     }

     // Additional specific settings for icicle charts
     if (chart_type.toLowerCase() === 'icicle') {
         // Only set default colorway if no custom colors are applied via marker.colors
         const hasCustomColors = Array.isArray(chartData) ?
             chartData.some(trace => trace.marker?.colors) :
             chartData?.marker?.colors;

         if (!hasCustomColors) {
             // Enhance icicle chart appearance with default colors only if no custom colors
             (baseLayout as any).iciclecolorway = [
                 '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                 '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
             ]; // Custom color palette
         }

         // Remove Total text and count
         (baseLayout as any).annotations = [];
     }

     // Special handling for charts with legends (stacked charts and multi-charts) - position legend to avoid overlap
     if (isChartWithLegend) {
         // Show legend for charts with multiple series
         (baseLayout as any).showlegend = true;

         // Position legend at the top in a single row (recommended for better space usage)
         baseLayout.legend = {
             ...baseLayout.legend,
             orientation: 'h', // Horizontal orientation for single row
             x: 0.5, // Center horizontally
             y: 1.05, // Position above the plot area
             xanchor: 'center', // Center anchor horizontally
             yanchor: 'bottom', // Bottom anchor
             bgcolor: 'rgba(255,255,255,0.9)',
             bordercolor: 'rgba(0, 0, 0, 0.1)',
             borderwidth: 1,
             font: { size: 9, color: '#212121' },
             // Ensure legend items are in a single row
             itemsizing: 'constant',
             itemwidth: 25
         };

         // Adjust top margin to accommodate legend
         baseLayout.margin = {
             ...baseLayout.margin,
             t: 60 // Top margin for legend space
         };

         // Option 2: Position legend on the right side (alternative - uncomment to use)
         /*
         baseLayout.legend = {
             ...baseLayout.legend,
             orientation: 'v', // Vertical orientation
             x: 1.02, // Position to the right of the plot area
             y: 0.5, // Center vertically
             xanchor: 'left', // Anchor to the left edge of the legend
             yanchor: 'middle', // Center anchor vertically
             bgcolor: 'rgba(255,255,255,0.9)',
             bordercolor: 'rgba(0, 0, 0, 0.1)',
             borderwidth: 1,
             font: { size: 10, color: '#212121' }
         };

         // Adjust right margin to accommodate legend
         baseLayout.margin = {
             ...baseLayout.margin,
             r: 120 // Increased right margin for legend space
         };
         */
     }

     // Special handling for horizontal bar charts - position legend to avoid overlap
     if (isHorizontalBar && !isPieOrDonut && !isHierarchyChart && !isSemiCircle) {
         // Show legend for horizontal bar charts if there are multiple series
         const hasMultipleSeries = Array.isArray(chartData) && chartData.length > 1;

         if (hasMultipleSeries) {
             (baseLayout as any).showlegend = true;

             // Position legend at the top to avoid overlap with horizontal bars
             baseLayout.legend = {
                 ...baseLayout.legend,
                 orientation: 'h', // Horizontal orientation for single row
                 x: 0.5, // Center horizontally
                 y: 1.05, // Position above the plot area
                 xanchor: 'center', // Center anchor horizontally
                 yanchor: 'bottom', // Bottom anchor
                 bgcolor: 'rgba(255,255,255,0.9)',
                 bordercolor: 'rgba(0, 0, 0, 0.1)',
                 borderwidth: 1,
                 font: { size: 9, color: '#212121' },
                 // Ensure legend items are in a single row
                 itemsizing: 'constant',
                 itemwidth: 25
             };

             // Adjust top margin to accommodate legend
             baseLayout.margin = {
                 ...baseLayout.margin,
                 t: 60, // Top margin for legend space
                 l: 80  // Increased left margin for horizontal bar labels
             };
         } else {
             // Single series horizontal bar - no legend needed, but increase left margin for labels
             baseLayout.margin = {
                 ...baseLayout.margin,
                 l: 80  // Increased left margin for horizontal bar labels
             };
         }
     }

    return baseLayout;
  }, [layout, chartTitle, xAxisLabel, yAxisLabel, chart_type]);
   // --- End Get Layout ---

  // Handle potential errors or empty data
  if (!chartData || (Array.isArray(chartData) && chartData.length === 0) || (!Array.isArray(chartData) && Object.keys(chartData).length === 0)) {
     return <Box sx={{ p: 3, color: 'text.secondary', textAlign: 'center' }}>No data available for this chart.</Box>;
   }

  // Helper function to capitalize first letter
  const capitalizeFirstLetter = (str: string) => {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  return (
    <Box
      ref={plotContainerRef}
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        p: 0.25, // Reduced padding from 0.5 to 0.25
        gap: 0.25, // Reduced gap from 0.5 to 0.25
        overflow: 'hidden', // Prevent scrollbars
        maxWidth: '100%' // Ensure container doesn't exceed parent width
      }}
    >
      {/* Chart Title Row */}
      <Box sx={{
        height: '32px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        px: 1,
        flexShrink: 0
      }}>
        <EditableText
          value={capitalizeFirstLetter(chartTitle)}
          onChange={(newTitle) => handleTitleChange(capitalizeFirstLetter(newTitle))}
          variant="title"
          placeholder="Add Chart Title"
        />
      </Box>

      {/* Main Chart Area with Y-Axis Title and Chart */}
      <Box sx={{
        flex: 1,
        display: 'flex',
        alignItems: 'stretch',
        minHeight: 0,
        position: 'relative'
      }}>
        {/* Y-Axis Label - Hidden for pie/donut/doughnut/semi_circle charts, hierarchy charts, and scatter matrix */}
        {chart_type !== 'pie' && !['donut', 'doughnut', 'semi_circle'].includes(chart_type.toLowerCase()) && !['treemap', 'sunburst', 'icicle'].includes(chart_type.toLowerCase()) && !['splom', 'scattermatrix', 'scatter_matrix'].includes(chart_type.toLowerCase()) && yAxisLabel && (
          <Box sx={{
            width: '20px', // Reduced from 32px to 20px
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexShrink: 0,
            zIndex: 1
          }}>
            <Box sx={{
              transform: 'rotate(-90deg)',
              whiteSpace: 'nowrap',
              fontSize: '0.875rem'
            }}>
              <EditableText
                value={capitalizeFirstLetter(yAxisLabel)}
                onChange={(newLabel) => handleYAxisChange(capitalizeFirstLetter(newLabel))}
                variant="axis"
                placeholder=""
              />
            </Box>
          </Box>
        )}

        {/* Chart Container */}
        <Box sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0,
          overflow: 'hidden'
        }}>
          {/* Plotly Chart */}
          <Box sx={{
            flex: 1,
            width: '100%',
            maxWidth: '100%',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <Plot
              key={`${chart_type}-${data.id}-${forceRender}`} // Force re-render when chart type changes
              data={(() => {
                const plotData = prepareData();
                console.log('Plot data for semi-circle:', plotData);
                return plotData;
              })()}
              layout={(() => {
                const plotLayout = getLayout();
                console.log('Plot layout for semi-circle:', plotLayout);
                return plotLayout;
              })()}
              useResizeHandler={true}
              style={{ width: '100%', height: '100%', maxWidth: '100%' }}
              config={{
                responsive: true,
                displayModeBar: false,
                displaylogo: false,
                autosizable: true,
              }}
            />
          </Box>
        </Box>
      </Box>

      {/* X-Axis Label - Hidden for pie/donut/doughnut/semi_circle charts, hierarchy charts, and scatter matrix */}
      {chart_type !== 'pie' && !['donut', 'doughnut', 'semi_circle'].includes(chart_type.toLowerCase()) && !['treemap', 'sunburst', 'icicle'].includes(chart_type.toLowerCase()) && !['splom', 'scattermatrix', 'scatter_matrix'].includes(chart_type.toLowerCase()) && xAxisLabel && (
        <Box sx={{
          height: '16px', // Reduced from 24px to 16px
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          px: 0.5, // Reduced padding from 1 to 0.5
          flexShrink: 0,
          fontSize: '0.875rem'
        }}>
          <EditableText
            value={capitalizeFirstLetter(xAxisLabel)}
            onChange={(newLabel) => handleXAxisChange(capitalizeFirstLetter(newLabel))}
            variant="axis"
            placeholder=""
          />
        </Box>
      )}

      {/* Action Buttons Row - Below X-axis title */}
      <Box
        className="action-buttons"
        sx={{
          height: '28px', // Reduced from 32px to 28px
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-start', // Changed from 'center' to 'flex-start' for left alignment
          gap: 0.5,
          px: 0.5, // Reduced padding from 1 to 0.5
          flexShrink: 0,
          flexWrap: 'wrap'
        }}>
        {/* Insights Button - Only show if not hidden */}
        {!hideInsightsIcon && (
          <Box
            component="button"
            onClick={handleInsightsClick}
            title="View Insights"
            sx={{
              cursor: 'pointer',
              background: 'rgba(255, 255, 255, 0.95)',
              border: '1px solid rgba(0, 0, 0, 0.08)',
              padding: '3px',
              borderRadius: '3px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
              minWidth: '24px',
              minHeight: '24px',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
              }
            }}
          >
            <InsightsIcon sx={{ fontSize: '1rem', color: '#2196f3' }} />
          </Box>
        )}

        {/* Preview Button - Only show if onPreviewClick is provided */}
        {onPreviewClick && (
          <Box
            component="button"
            onClick={handlePreviewClick}
            title="Preview Chart"
            sx={{
              cursor: 'pointer',
              background: 'rgba(255, 255, 255, 0.95)',
              border: '1px solid rgba(0, 0, 0, 0.08)',
              padding: '3px',
              borderRadius: '3px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
              minWidth: '24px',
              minHeight: '24px',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
              }
            }}
          >
            <VisibilityIcon sx={{ fontSize: '1rem', color: '#9c27b0' }} />
          </Box>
        )}

        {/* Modify Button - Only show if onModifyClick is provided */}
        {onModifyClick && (
          <Box
            component="button"
            onClick={handleModifyClick}
            title="Modify Chart"
            sx={{
              cursor: 'pointer',
              background: 'rgba(255, 255, 255, 0.95)',
              border: '1px solid rgba(0, 0, 0, 0.08)',
              padding: '3px',
              borderRadius: '3px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
              minWidth: '24px',
              minHeight: '24px',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
              }
            }}
          >
            <EditIcon sx={{ fontSize: '1rem', color: '#f57c00' }} />
          </Box>
        )}

        {/* Copy to Clipboard Button */}
        <Box
          component="button"
          onClick={handleCopyChart}
          data-copy-button={`chart-${data.id}`}
          title="Copy to Clipboard"
          sx={{
            cursor: 'pointer',
            background: 'rgba(255, 255, 255, 0.95)',
            border: '1px solid rgba(0, 0, 0, 0.08)',
            padding: '3px',
            borderRadius: '3px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
            minWidth: '24px',
            minHeight: '24px',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
            }
          }}
        >
          <ContentCopyIcon sx={{ fontSize: '1rem', color: '#4caf50' }} />
        </Box>

        {/* Export Button */}
        <Box
          component="button"
          onClick={handleSaveChart}
          title="Export as PNG"
          sx={{
            cursor: 'pointer',
            background: 'rgba(255, 255, 255, 0.95)',
            border: '1px solid rgba(0, 0, 0, 0.08)',
            padding: '3px',
            borderRadius: '3px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
            minWidth: '24px',
            minHeight: '24px',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
            }
          }}
        >
          <DownloadIcon sx={{ fontSize: '1rem', color: '#ff9800' }} />
        </Box>
      </Box>
    </Box>
  );
};

export default Chart;

