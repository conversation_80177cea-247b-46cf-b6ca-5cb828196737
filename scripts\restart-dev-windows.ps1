# PowerShell script to restart development server on Windows
# This helps resolve EMFILE (too many open files) errors

Write-Host "🔄 Restarting development server for Windows..." -ForegroundColor Cyan

# Kill any existing Node.js processes that might be holding file handles
Write-Host "🛑 Stopping existing Node.js processes..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

# Kill any Vite processes
Write-Host "🛑 Stopping existing Vite processes..." -ForegroundColor Yellow
Get-Process | Where-Object {$_.ProcessName -like "*vite*" -or $_.CommandLine -like "*vite*"} | Stop-Process -Force -ErrorAction SilentlyContinue

# Wait a moment for processes to fully terminate
Start-Sleep -Seconds 2

# Clear npm cache to reduce file system pressure
Write-Host "🧹 Clearing npm cache..." -ForegroundColor Yellow
npm cache clean --force

# Clear Vite cache
Write-Host "🧹 Clearing Vite cache..." -ForegroundColor Yellow
if (Test-Path "node_modules/.vite") {
    Remove-Item -Recurse -Force "node_modules/.vite"
}

# Clear dist folder
Write-Host "🧹 Clearing dist folder..." -ForegroundColor Yellow
if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist"
}

Write-Host "✅ Cleanup complete!" -ForegroundColor Green

# Start the development server with Windows optimizations
Write-Host "🚀 Starting development server with Windows optimizations..." -ForegroundColor Green
$env:NODE_OPTIONS = "--max-old-space-size=4096"
npm run dev

Write-Host "🎉 Development server started!" -ForegroundColor Green
