import { createTheme } from '@mui/material';

// Design System Colors
export const colors = {
  background: '#FFFFFF',
  textPrimary: '#2E2E2E',
  textSecondary: '#7A7A7A',
  accentPrimary: '#3B82F6',
  accentSecondary: '#10B981',
};

export const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: colors.accentPrimary, // Sky Blue
      light: '#60A5FA',
      dark: '#2563EB',
    },
    secondary: {
      main: colors.accentSecondary, // Emerald Green
      light: '#34D399',
      dark: '#059669',
    },
    background: {
      default: colors.background,
      paper: colors.background,
    },
    text: {
      primary: colors.textPrimary,
      secondary: colors.textSecondary,
    },
  },
  typography: {
    fontFamily: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      marginBottom: '1rem',
      color: colors.textPrimary,
      letterSpacing: '-0.02em',
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      color: colors.textPrimary,
      letterSpacing: '-0.01em',
    },
    h3: {
      fontSize: '1.5rem',
      fontWeight: 600,
      color: colors.textPrimary,
      letterSpacing: '-0.01em',
    },
    h4: {
      fontSize: '1.25rem',
      fontWeight: 600,
      color: colors.textPrimary,
      letterSpacing: '-0.01em',
    },
    h5: {
      fontSize: '1.125rem',
      fontWeight: 600,
      color: colors.textPrimary,
      letterSpacing: '-0.01em',
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
      color: colors.textPrimary,
      letterSpacing: '-0.01em',
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      color: colors.textPrimary,
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      color: colors.textSecondary,
      lineHeight: 1.5,
    },
    subtitle1: {
      fontSize: '1rem',
      fontWeight: 500,
      color: colors.textPrimary,
      lineHeight: 1.5,
    },
    subtitle2: {
      fontSize: '0.875rem',
      fontWeight: 500,
      color: colors.textSecondary,
      lineHeight: 1.4,
    },
    caption: {
      fontSize: '0.75rem',
      fontWeight: 400,
      color: colors.textSecondary,
      lineHeight: 1.4,
    },
  },
  components: {
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          backgroundColor: colors.background,
          boxShadow: '0 1px 3px rgba(46, 46, 46, 0.1)',
          borderRadius: '12px',
          border: `1px solid rgba(46, 46, 46, 0.06)`,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
          borderRadius: '8px',
          fontSize: '0.95rem',
          fontFamily: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
          transition: 'all 0.2s ease-in-out',
        },
        contained: {
          backgroundColor: colors.accentPrimary,
          color: '#FFFFFF',
          boxShadow: `0 2px 4px rgba(59, 130, 246, 0.2)`,
          '&:hover': {
            backgroundColor: '#2563EB',
            boxShadow: `0 4px 8px rgba(59, 130, 246, 0.3)`,
            transform: 'translateY(-1px)',
          },
        },
        outlined: {
          borderColor: 'rgba(122, 122, 122, 0.3)',
          color: colors.textSecondary,
          '&:hover': {
            borderColor: colors.accentPrimary,
            backgroundColor: `rgba(59, 130, 246, 0.04)`,
            color: colors.accentPrimary,
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: colors.background,
          color: colors.textPrimary,
          boxShadow: '0 1px 3px rgba(46, 46, 46, 0.1)',
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          fontFamily: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
        },
      },
    },
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          fontFamily: '"Manrope", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
          backgroundColor: colors.background,
          color: colors.textPrimary,
        },
      },
    },
  },
});
