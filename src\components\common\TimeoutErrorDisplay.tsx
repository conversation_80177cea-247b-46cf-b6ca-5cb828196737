import React, { useState, useEffect } from 'react';
import { Box, Typography, Alert, AlertTitle, LinearProgress, Chip } from '@mui/material';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import RefreshIcon from '@mui/icons-material/Refresh';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import WifiOffIcon from '@mui/icons-material/WifiOff';
import ProfessionalButton from './ProfessionalButton';
import TimeoutInfo from './TimeoutInfo';
import config from '../../config';

interface TimeoutErrorDisplayProps {
  onRetry?: () => void;
  onGoBack?: () => void;
  variant?: 'inline' | 'page' | 'modal';
  title?: string;
  message?: string;
  showProgress?: boolean;
  timeoutDuration?: number;
  operationType?: 'upload' | 'insights' | 'chart_update' | 'general';
  className?: string;
}

const TimeoutErrorDisplay: React.FC<TimeoutErrorDisplayProps> = ({
  onRetry,
  onGoBack,
  variant = 'page',
  title,
  message,
  showProgress = false,
  timeoutDuration = config.apiTimeout,
  operationType = 'general',
  className
}) => {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  // Get operation-specific messages
  const getOperationMessages = () => {
    switch (operationType) {
      case 'upload':
        return {
          defaultTitle: 'File Upload Timeout',
          defaultMessage: 'Your file upload is taking longer than expected. Large files may require more time to process. Please check your internet connection and try again.',
          suggestions: [
            'Check your internet connection',
            'Try uploading a smaller file',
            'Ensure your file is in a supported format (CSV, Excel)',
            'Try again during off-peak hours'
          ]
        };
      case 'insights':
        return {
          defaultTitle: 'Insights Generation Timeout',
          defaultMessage: 'Generating insights is taking longer than expected. Complex data analysis may require more processing time.',
          suggestions: [
            'The server may be processing complex data',
            'Try again in a few moments',
            'Check your internet connection',
            'Consider simplifying your data if possible'
          ]
        };
      case 'chart_update':
        return {
          defaultTitle: 'Chart Update Timeout',
          defaultMessage: 'Updating the chart is taking longer than expected. Please try again.',
          suggestions: [
            'Check your internet connection',
            'Try refreshing the page',
            'The server may be temporarily busy'
          ]
        };
      default:
        return {
          defaultTitle: 'Request Timeout',
          defaultMessage: 'The request is taking longer than expected. Please check your internet connection and try again.',
          suggestions: [
            'Check your internet connection',
            'Try again in a few moments',
            'The server may be temporarily busy'
          ]
        };
    }
  };

  const { defaultTitle, defaultMessage, suggestions } = getOperationMessages();
  const displayTitle = title || defaultTitle;
  const displayMessage = message || defaultMessage;

  const handleRetry = async () => {
    if (!onRetry) return;
    
    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    
    try {
      await onRetry();
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  // Auto-retry logic for certain operations
  useEffect(() => {
    if (operationType === 'insights' && retryCount === 0 && onRetry) {
      // Auto-retry once for insights after 3 seconds
      const timer = setTimeout(() => {
        handleRetry();
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [operationType, retryCount, onRetry]);

  // Inline variant (for small spaces)
  if (variant === 'inline') {
    return (
      <Alert 
        severity="warning"
        className={className}
        icon={<AccessTimeIcon />}
        sx={{
          '& .MuiAlert-message': {
            width: '100%'
          }
        }}
        action={
          onRetry && (
            <ProfessionalButton
              size="small"
              onClick={handleRetry}
              disabled={isRetrying}
              startIcon={isRetrying ? undefined : <RefreshIcon />}
            >
              {isRetrying ? 'Retrying...' : 'Retry'}
            </ProfessionalButton>
          )
        }
      >
        <AlertTitle sx={{ fontSize: '0.875rem', fontWeight: 600 }}>
          {displayTitle}
        </AlertTitle>
        <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
          {displayMessage}
        </Typography>
      </Alert>
    );
  }

  // Modal variant (for modals/dialogs)
  if (variant === 'modal') {
    return (
      <Box className={className} sx={{ p: 3, textAlign: 'center' }}>
        <AccessTimeIcon sx={{ fontSize: '3rem', color: '#ff9800', mb: 2 }} />
        
        <Typography variant="h6" sx={{ color: '#e65100', mb: 2, fontWeight: 600 }}>
          {displayTitle}
        </Typography>
        
        <Typography variant="body2" sx={{ color: '#2d3748', mb: 3 }}>
          {displayMessage}
        </Typography>
        
        {showProgress && isRetrying && (
          <Box sx={{ mb: 3 }}>
            <LinearProgress />
            <Typography variant="body2" sx={{ mt: 1, color: '#666' }}>
              Retrying... (Attempt {retryCount + 1})
            </Typography>
          </Box>
        )}
        
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
          {onRetry && (
            <ProfessionalButton
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={handleRetry}
              disabled={isRetrying}
            >
              {isRetrying ? 'Retrying...' : 'Try Again'}
            </ProfessionalButton>
          )}
          {onGoBack && (
            <ProfessionalButton
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={onGoBack}
            >
              Go Back
            </ProfessionalButton>
          )}
        </Box>
      </Box>
    );
  }

  // Page variant (full page error display)
  return (
    <Box 
      className={className}
      sx={{ 
        width: '100%', 
        height: '100%',
        minHeight: '400px',
        display: 'flex', 
        flexDirection: 'column', 
        justifyContent: 'center', 
        alignItems: 'center', 
        p: 3 
      }}
    >
      <Box sx={{ 
        textAlign: 'center', 
        maxWidth: 700, 
        p: 4, 
        borderRadius: 3, 
        backgroundColor: '#fff8e1', 
        border: '1px solid #ffcc02',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
      }}>
        {/* Timeout Icon */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          mb: 2 
        }}>
          <AccessTimeIcon sx={{ 
            fontSize: '4rem',
            color: '#ff9800'
          }} />
        </Box>

        {/* Title */}
        <Typography variant="h4" sx={{ 
          color: '#e65100', 
          mb: 2, 
          fontWeight: 600 
        }}>
          {displayTitle}
        </Typography>

        {/* Message */}
        <Typography variant="body1" sx={{ 
          color: '#2d3748', 
          mb: 3, 
          lineHeight: 1.6 
        }}>
          {displayMessage}
        </Typography>

        {/* Timeout Info */}
        <Alert severity="info" sx={{ mb: 3, textAlign: 'left' }}>
          <AlertTitle>Timeout Information</AlertTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <Chip 
              label={`Timeout: ${timeoutDuration / 1000}s`} 
              size="small" 
              color="primary" 
            />
            {retryCount > 0 && (
              <Chip 
                label={`Attempts: ${retryCount + 1}`} 
                size="small" 
                color="warning" 
              />
            )}
          </Box>
          <Typography variant="body2">
            Requests automatically timeout after {timeoutDuration / 1000} seconds to prevent hanging.
          </Typography>
        </Alert>

        {/* Timeout Information and Troubleshooting */}
        <TimeoutInfo
          variant="detailed"
          operationType={operationType}
          showConfiguration={true}
        />

        {/* Progress indicator during retry */}
        {showProgress && isRetrying && (
          <Box sx={{ mb: 3 }}>
            <LinearProgress />
            <Typography variant="body2" sx={{ mt: 1, color: '#666' }}>
              Retrying... (Attempt {retryCount + 1})
            </Typography>
          </Box>
        )}

        {/* Action Buttons */}
        <Box sx={{
          display: 'flex',
          gap: 2,
          justifyContent: 'center',
          flexWrap: 'wrap'
        }}>
          {onRetry && (
            <ProfessionalButton
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={handleRetry}
              disabled={isRetrying}
              color="primary"
            >
              {isRetrying ? 'Retrying...' : 'Try Again'}
            </ProfessionalButton>
          )}
          
          {onGoBack && (
            <ProfessionalButton
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={onGoBack}
            >
              Go Back
            </ProfessionalButton>
          )}
        </Box>

        {/* Network status indicator */}
        {!navigator.onLine && (
          <Alert severity="error" sx={{ mt: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <WifiOffIcon />
              <Typography variant="body2">
                No internet connection detected. Please check your network connection.
              </Typography>
            </Box>
          </Alert>
        )}
      </Box>
    </Box>
  );
};

export default TimeoutErrorDisplay;
