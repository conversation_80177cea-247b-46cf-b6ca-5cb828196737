const config = {
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/v1',
  googleClientId: import.meta.env.VITE_GOOGLE_CLIENT_ID || 'YOUR_DEFAULT_CLIENT_ID',
  apiTimeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000', 10), // Default 30 seconds
  showTechnicalDetails: import.meta.env.VITE_SHOW_TECHNICAL_DETAILS === 'true' || import.meta.env.MODE === 'development', // Show technical details in development or when explicitly enabled

  // Loading Steps Configuration
  uploadStepDuration: parseInt(import.meta.env.VITE_UPLOAD_STEP_DURATION || '400', 10), // Default 400ms per upload step
  insightsStepDuration: parseInt(import.meta.env.VITE_INSIGHTS_STEP_DURATION || '1200', 10), // Default 1200ms per insights step
};

export default config;
