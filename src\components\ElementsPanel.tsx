import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import ImageIcon from '@mui/icons-material/Image';
import TextFieldsIcon from '@mui/icons-material/TextFields';

interface ElementsPanelProps {
  onAddLogo: (file: File) => void;
  onAddTextBox: () => void;
}

const ElementsPanel: React.FC<ElementsPanelProps> = ({ onAddLogo, onAddTextBox }) => {
  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onAddLogo(file);
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" sx={{ mb: 2 }}>Elements</Typography>
      
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 2 }}>Logo</Typography>
        <input
          accept="image/*"
          style={{ display: 'none' }}
          id="logo-upload"
          type="file"
          onChange={handleLogoUpload}
        />
        <label htmlFor="logo-upload">
          <Button
            variant="outlined"
            component="span"
            startIcon={<ImageIcon />}
            fullWidth
          >
            Upload Logo
          </Button>
        </label>
      </Paper>

      <Paper sx={{ p: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 2 }}>Text</Typography>
        <Button
          variant="outlined"
          startIcon={<TextFieldsIcon />}
          onClick={onAddTextBox}
          fullWidth
        >
          Add Text Box
        </Button>
      </Paper>
    </Box>
  );
};

export default ElementsPanel;